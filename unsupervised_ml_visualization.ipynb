# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.cluster import KMeans, AgglomerativeClustering, DBSCAN
from sklearn.mixture import GaussianMixture
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score, calinski_harabasz_score, silhouette_samples
from scipy.cluster.hierarchy import dendrogram, linkage
import glob
import os
import warnings
warnings.filterwarnings('ignore')

# Set style for better plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

print("Libraries imported successfully!")

def load_and_preprocess_data(data_path="data", sample_size=None):
    """Load and preprocess data from all files"""
    print("Loading data from all files...")
    
    # Get all txt files
    file_pattern = os.path.join(data_path, "*.txt")
    files = glob.glob(file_pattern)
    print(f"Found {len(files)} files to process")
    
    all_data = []
    for i, file_path in enumerate(files):
        try:
            df = pd.read_csv(file_path, sep='\t')
            df['file_id'] = i
            df['file_name'] = os.path.basename(file_path)
            all_data.append(df)
            if i % 20 == 0:
                print(f"Processed {i+1}/{len(files)} files")
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
            continue
    
    # Combine all data
    raw_data = pd.concat(all_data, ignore_index=True)
    print(f"Total data shape: {raw_data.shape}")
    
    # Select numeric features for clustering
    numeric_cols = raw_data.select_dtypes(include=[np.number]).columns
    feature_cols = [col for col in numeric_cols if col not in ['file_id']]
    
    # Extract and clean features
    features = raw_data[feature_cols].copy()
    features = features.replace([np.inf, -np.inf], np.nan)
    features = features.fillna(features.mean())
    
    # Feature engineering
    print("Creating engineered features...")
    features['temp_diff_PM_TM'] = features['T_PM'] - features['T_TM']
    features['temp_diff_PM_in'] = features['T_PM'] - features['T_PM_in']
    features['d90_d10_ratio'] = features['d90'] / (features['d10'] + 1e-8)
    features['mf_ratio'] = features['mf_PM'] / (features['mf_TM'] + 1e-8)
    features['c_diff'] = features['c'] - features['c_in']
    
    # Handle any new infinite values
    features = features.replace([np.inf, -np.inf], np.nan)
    features = features.fillna(features.mean())
    
    # Scale features
    scaler = StandardScaler()
    scaled_features = scaler.fit_transform(features)
    
    # Sample data if requested (for memory management)
    if sample_size and sample_size < len(scaled_features):
        np.random.seed(42)
        indices = np.random.choice(len(scaled_features), sample_size, replace=False)
        scaled_features = scaled_features[indices]
        raw_data = raw_data.iloc[indices].reset_index(drop=True)
        print(f"Sampled {sample_size} data points")
    
    return scaled_features, raw_data, feature_cols, scaler

# Load data
features, raw_data, feature_names, scaler = load_and_preprocess_data(sample_size=15000)
print(f"\nFinal dataset shape: {features.shape}")
print(f"Feature names: {feature_names}")

# Basic data exploration
fig, axes = plt.subplots(2, 2, figsize=(15, 10))
fig.suptitle('Data Exploration', fontsize=16, fontweight='bold')

# Distribution of key variables
key_vars = ['c', 'T_PM', 'd50', 'mf_PM']
for i, var in enumerate(key_vars):
    ax = axes[i//2, i%2]
    raw_data[var].hist(bins=50, ax=ax, alpha=0.7)
    ax.set_title(f'Distribution of {var}')
    ax.set_xlabel(var)
    ax.set_ylabel('Frequency')

plt.tight_layout()
plt.show()

# Correlation matrix
plt.figure(figsize=(12, 10))
correlation_matrix = pd.DataFrame(features, columns=feature_names + ['temp_diff_PM_TM', 'temp_diff_PM_in', 'd90_d10_ratio', 'mf_ratio', 'c_diff']).corr()
sns.heatmap(correlation_matrix, annot=False, cmap='coolwarm', center=0, square=True)
plt.title('Feature Correlation Matrix', fontsize=14, fontweight='bold')
plt.tight_layout()
plt.show()

def apply_and_visualize_kmeans(features, n_clusters=2):
    """Apply K-Means clustering and create visualizations"""
    print(f"Applying K-Means clustering with {n_clusters} clusters...")
    
    # Apply K-Means
    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
    labels = kmeans.fit_predict(features)
    
    # Calculate metrics
    silhouette_avg = silhouette_score(features, labels)
    calinski = calinski_harabasz_score(features, labels)
    
    print(f"Silhouette Score: {silhouette_avg:.4f}")
    print(f"Calinski-Harabasz Score: {calinski:.4f}")
    print(f"Inertia: {kmeans.inertia_:.4f}")
    
    # Create visualizations
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('K-Means Clustering Results', fontsize=16, fontweight='bold')
    
    # PCA for visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Scatter plot of clusters
    scatter = axes[0,0].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
    axes[0,0].set_title('K-Means Clusters (PCA Projection)')
    axes[0,0].set_xlabel(f'PC1 ({pca_viz.explained_variance_ratio_[0]:.3f} variance)')
    axes[0,0].set_ylabel(f'PC2 ({pca_viz.explained_variance_ratio_[1]:.3f} variance)')
    plt.colorbar(scatter, ax=axes[0,0])
    
    # Cluster centers in PCA space
    centers_2d = pca_viz.transform(kmeans.cluster_centers_)
    axes[0,0].scatter(centers_2d[:, 0], centers_2d[:, 1], c='red', marker='x', s=200, linewidths=3, label='Centroids')
    axes[0,0].legend()
    
    # Silhouette analysis
    silhouette_vals = silhouette_samples(features, labels)
    y_lower = 10
    for i in range(n_clusters):
        cluster_silhouette_vals = silhouette_vals[labels == i]
        cluster_silhouette_vals.sort()
        
        size_cluster_i = cluster_silhouette_vals.shape[0]
        y_upper = y_lower + size_cluster_i
        
        color = plt.cm.viridis(float(i) / n_clusters)
        axes[0,1].fill_betweenx(np.arange(y_lower, y_upper), 0, cluster_silhouette_vals,
                               facecolor=color, edgecolor=color, alpha=0.7)
        
        axes[0,1].text(-0.05, y_lower + 0.5 * size_cluster_i, str(i))
        y_lower = y_upper + 10
    
    axes[0,1].set_xlabel('Silhouette coefficient values')
    axes[0,1].set_ylabel('Cluster label')
    axes[0,1].set_title('Silhouette Analysis')
    axes[0,1].axvline(x=silhouette_avg, color="red", linestyle="--", label=f'Average Score: {silhouette_avg:.3f}')
    axes[0,1].legend()
    
    # Cluster size distribution
    unique, counts = np.unique(labels, return_counts=True)
    axes[1,0].bar(unique, counts, color=['skyblue', 'lightcoral'][:len(unique)])
    axes[1,0].set_title('Cluster Size Distribution')
    axes[1,0].set_xlabel('Cluster')
    axes[1,0].set_ylabel('Number of Points')
    for i, count in enumerate(counts):
        axes[1,0].text(i, count + 50, str(count), ha='center')
    
    # Elbow method for optimal k
    k_range = range(1, 11)
    inertias = []
    for k in k_range:
        kmeans_temp = KMeans(n_clusters=k, random_state=42, n_init=10)
        kmeans_temp.fit(features)
        inertias.append(kmeans_temp.inertia_)
    
    axes[1,1].plot(k_range, inertias, 'bo-')
    axes[1,1].set_title('Elbow Method for Optimal k')
    axes[1,1].set_xlabel('Number of Clusters (k)')
    axes[1,1].set_ylabel('Inertia')
    axes[1,1].axvline(x=n_clusters, color='red', linestyle='--', label=f'Selected k={n_clusters}')
    axes[1,1].legend()
    
    plt.tight_layout()
    plt.show()
    
    return labels, {'silhouette': silhouette_avg, 'calinski': calinski, 'inertia': kmeans.inertia_}

# Apply K-Means
kmeans_labels, kmeans_metrics = apply_and_visualize_kmeans(features)

def apply_and_visualize_gmm(features, n_components=2):
    """Apply GMM clustering and create visualizations"""
    print(f"Applying GMM clustering with {n_components} components...")
    
    # Apply GMM
    gmm = GaussianMixture(n_components=n_components, random_state=42)
    labels = gmm.fit_predict(features)
    
    # Calculate metrics
    silhouette_avg = silhouette_score(features, labels)
    calinski = calinski_harabasz_score(features, labels)
    aic = gmm.aic(features)
    bic = gmm.bic(features)
    
    print(f"Silhouette Score: {silhouette_avg:.4f}")
    print(f"Calinski-Harabasz Score: {calinski:.4f}")
    print(f"AIC: {aic:.4f}")
    print(f"BIC: {bic:.4f}")
    
    # Create visualizations
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Gaussian Mixture Model Results', fontsize=16, fontweight='bold')
    
    # PCA for visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Scatter plot of clusters
    scatter = axes[0,0].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
    axes[0,0].set_title('GMM Clusters (PCA Projection)')
    axes[0,0].set_xlabel(f'PC1 ({pca_viz.explained_variance_ratio_[0]:.3f} variance)')
    axes[0,0].set_ylabel(f'PC2 ({pca_viz.explained_variance_ratio_[1]:.3f} variance)')
    plt.colorbar(scatter, ax=axes[0,0])
    
    # Model selection (AIC/BIC)
    n_components_range = range(1, 11)
    aic_scores = []
    bic_scores = []
    
    for n in n_components_range:
        gmm_temp = GaussianMixture(n_components=n, random_state=42)
        gmm_temp.fit(features)
        aic_scores.append(gmm_temp.aic(features))
        bic_scores.append(gmm_temp.bic(features))
    
    axes[0,1].plot(n_components_range, aic_scores, 'bo-', label='AIC')
    axes[0,1].plot(n_components_range, bic_scores, 'ro-', label='BIC')
    axes[0,1].set_title('Model Selection (AIC/BIC)')
    axes[0,1].set_xlabel('Number of Components')
    axes[0,1].set_ylabel('Information Criterion')
    axes[0,1].axvline(x=n_components, color='green', linestyle='--', label=f'Selected k={n_components}')
    axes[0,1].legend()
    
    # Cluster size distribution
    unique, counts = np.unique(labels, return_counts=True)
    axes[1,0].bar(unique, counts, color=['skyblue', 'lightcoral'][:len(unique)])
    axes[1,0].set_title('Cluster Size Distribution')
    axes[1,0].set_xlabel('Cluster')
    axes[1,0].set_ylabel('Number of Points')
    for i, count in enumerate(counts):
        axes[1,0].text(i, count + 50, str(count), ha='center')
    
    # Probability distribution
    probabilities = gmm.predict_proba(features)
    axes[1,1].hist(probabilities[:, 0], bins=50, alpha=0.7, label='Component 0')
    if n_components > 1:
        axes[1,1].hist(probabilities[:, 1], bins=50, alpha=0.7, label='Component 1')
    axes[1,1].set_title('Component Probability Distribution')
    axes[1,1].set_xlabel('Probability')
    axes[1,1].set_ylabel('Frequency')
    axes[1,1].legend()
    
    plt.tight_layout()
    plt.show()
    
    return labels, {'silhouette': silhouette_avg, 'calinski': calinski, 'aic': aic, 'bic': bic}

# Apply GMM
gmm_labels, gmm_metrics = apply_and_visualize_gmm(features)

def apply_and_visualize_dbscan(features, eps=0.5, min_samples=5):
    """Apply DBSCAN clustering and create visualizations"""
    print(f"Applying DBSCAN clustering (eps={eps}, min_samples={min_samples})...")
    
    # Apply DBSCAN
    dbscan = DBSCAN(eps=eps, min_samples=min_samples)
    labels = dbscan.fit_predict(features)
    
    n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
    n_noise = list(labels).count(-1)
    
    print(f"Number of clusters: {n_clusters}")
    print(f"Number of noise points: {n_noise}")
    
    # Calculate metrics (only if we have clusters)
    if n_clusters > 1:
        mask = labels != -1
        if mask.sum() > 0:
            silhouette_avg = silhouette_score(features[mask], labels[mask])
            calinski = calinski_harabasz_score(features[mask], labels[mask])
            print(f"Silhouette Score: {silhouette_avg:.4f}")
            print(f"Calinski-Harabasz Score: {calinski:.4f}")
        else:
            silhouette_avg = -1
            calinski = -1
    else:
        silhouette_avg = -1
        calinski = -1
        print("Cannot calculate silhouette score (insufficient clusters)")
    
    # Create visualizations
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('DBSCAN Clustering Results', fontsize=16, fontweight='bold')
    
    # PCA for visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Scatter plot of clusters
    scatter = axes[0,0].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
    axes[0,0].set_title('DBSCAN Clusters (PCA Projection)')
    axes[0,0].set_xlabel(f'PC1 ({pca_viz.explained_variance_ratio_[0]:.3f} variance)')
    axes[0,0].set_ylabel(f'PC2 ({pca_viz.explained_variance_ratio_[1]:.3f} variance)')
    plt.colorbar(scatter, ax=axes[0,0])
    
    # Parameter sensitivity analysis
    eps_range = [0.1, 0.3, 0.5, 0.7, 1.0, 1.5, 2.0]
    n_clusters_list = []
    n_noise_list = []
    
    for eps_test in eps_range:
        dbscan_test = DBSCAN(eps=eps_test, min_samples=min_samples)
        labels_test = dbscan_test.fit_predict(features)
        n_clusters_test = len(set(labels_test)) - (1 if -1 in labels_test else 0)
        n_noise_test = list(labels_test).count(-1)
        n_clusters_list.append(n_clusters_test)
        n_noise_list.append(n_noise_test)
    
    axes[0,1].plot(eps_range, n_clusters_list, 'bo-', label='Number of Clusters')
    axes[0,1].set_title('Parameter Sensitivity (eps)')
    axes[0,1].set_xlabel('eps')
    axes[0,1].set_ylabel('Number of Clusters')
    axes[0,1].axvline(x=eps, color='red', linestyle='--', label=f'Selected eps={eps}')
    axes[0,1].legend()
    
    # Cluster size distribution
    unique, counts = np.unique(labels, return_counts=True)
    colors = ['red' if x == -1 else 'skyblue' for x in unique]  # Red for noise
    axes[1,0].bar(unique, counts, color=colors)
    axes[1,0].set_title('Cluster Size Distribution')
    axes[1,0].set_xlabel('Cluster (-1 = Noise)')
    axes[1,0].set_ylabel('Number of Points')
    for i, (cluster, count) in enumerate(zip(unique, counts)):
        axes[1,0].text(cluster, count + 50, str(count), ha='center')
    
    # Noise ratio vs eps
    noise_ratios = [n / len(features) for n in n_noise_list]
    axes[1,1].plot(eps_range, noise_ratios, 'ro-')
    axes[1,1].set_title('Noise Ratio vs eps')
    axes[1,1].set_xlabel('eps')
    axes[1,1].set_ylabel('Noise Ratio')
    axes[1,1].axvline(x=eps, color='blue', linestyle='--', label=f'Selected eps={eps}')
    axes[1,1].legend()
    
    plt.tight_layout()
    plt.show()
    
    return labels, {'silhouette': silhouette_avg, 'calinski': calinski, 'n_clusters': n_clusters, 'n_noise': n_noise}

# Apply DBSCAN with different parameters
dbscan_labels, dbscan_metrics = apply_and_visualize_dbscan(features, eps=1.0, min_samples=10)

def apply_pca_clustering(features, n_components=2):
    """Apply PCA then clustering methods"""
    print(f"Applying PCA with {n_components} components...")
    
    # Apply PCA
    pca = PCA(n_components=n_components, random_state=42)
    features_pca = pca.fit_transform(features)
    
    print(f"Explained variance ratio: {pca.explained_variance_ratio_}")
    print(f"Total explained variance: {pca.explained_variance_ratio_.sum():.4f}")
    
    # Apply clustering methods on PCA features
    kmeans_pca = KMeans(n_clusters=2, random_state=42)
    kmeans_pca_labels = kmeans_pca.fit_predict(features_pca)
    
    gmm_pca = GaussianMixture(n_components=2, random_state=42)
    gmm_pca_labels = gmm_pca.fit_predict(features_pca)
    
    # Calculate metrics
    kmeans_pca_silhouette = silhouette_score(features_pca, kmeans_pca_labels)
    gmm_pca_silhouette = silhouette_score(features_pca, gmm_pca_labels)
    
    print(f"K-Means + PCA Silhouette Score: {kmeans_pca_silhouette:.4f}")
    print(f"GMM + PCA Silhouette Score: {gmm_pca_silhouette:.4f}")
    
    # Visualizations
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('PCA + Clustering Results', fontsize=16, fontweight='bold')
    
    # PCA explained variance
    axes[0,0].bar(range(1, len(pca.explained_variance_ratio_) + 1), pca.explained_variance_ratio_)
    axes[0,0].set_title('PCA Explained Variance Ratio')
    axes[0,0].set_xlabel('Principal Component')
    axes[0,0].set_ylabel('Explained Variance Ratio')
    
    # K-Means on PCA
    scatter1 = axes[0,1].scatter(features_pca[:, 0], features_pca[:, 1], c=kmeans_pca_labels, cmap='viridis', alpha=0.6)
    axes[0,1].set_title(f'K-Means on PCA (Silhouette: {kmeans_pca_silhouette:.3f})')
    axes[0,1].set_xlabel('PC1')
    axes[0,1].set_ylabel('PC2')
    plt.colorbar(scatter1, ax=axes[0,1])
    
    # GMM on PCA
    scatter2 = axes[1,0].scatter(features_pca[:, 0], features_pca[:, 1], c=gmm_pca_labels, cmap='viridis', alpha=0.6)
    axes[1,0].set_title(f'GMM on PCA (Silhouette: {gmm_pca_silhouette:.3f})')
    axes[1,0].set_xlabel('PC1')
    axes[1,0].set_ylabel('PC2')
    plt.colorbar(scatter2, ax=axes[1,0])
    
    # Feature importance (PCA components)
    feature_importance = np.abs(pca.components_).mean(axis=0)
    feature_names_eng = feature_names + ['temp_diff_PM_TM', 'temp_diff_PM_in', 'd90_d10_ratio', 'mf_ratio', 'c_diff']
    sorted_idx = np.argsort(feature_importance)[::-1][:10]  # Top 10 features
    
    axes[1,1].barh(range(len(sorted_idx)), feature_importance[sorted_idx])
    axes[1,1].set_yticks(range(len(sorted_idx)))
    axes[1,1].set_yticklabels([feature_names_eng[i] for i in sorted_idx])
    axes[1,1].set_title('Feature Importance in PCA')
    axes[1,1].set_xlabel('Importance')
    
    plt.tight_layout()
    plt.show()
    
    return {
        'pca_features': features_pca,
        'kmeans_labels': kmeans_pca_labels,
        'gmm_labels': gmm_pca_labels,
        'kmeans_silhouette': kmeans_pca_silhouette,
        'gmm_silhouette': gmm_pca_silhouette,
        'explained_variance': pca.explained_variance_ratio_
    }

# Apply PCA + Clustering
pca_results = apply_pca_clustering(features)

def compare_kmeans_vs_pca_kmeans(features, raw_data, kmeans_labels, pca_kmeans_labels):
    """Comprehensive comparison between K-Means and PCA+K-Means"""
    print("\n" + "="*80)
    print("DETAILED COMPARISON: K-MEANS vs PCA+K-MEANS")
    print("="*80)
    
    # Calculate metrics for both methods
    kmeans_silhouette = silhouette_score(features, kmeans_labels)
    pca_silhouette = silhouette_score(pca_results['pca_features'], pca_kmeans_labels)
    
    kmeans_calinski = calinski_harabasz_score(features, kmeans_labels)
    pca_calinski = calinski_harabasz_score(pca_results['pca_features'], pca_kmeans_labels)
    
    print(f"\n📊 PERFORMANCE METRICS COMPARISON:")
    print(f"{'Method':<20} {'Silhouette Score':<18} {'Calinski-Harabasz':<20} {'Explained Variance':<18}")
    print("-" * 80)
    print(f"{'K-Means':<20} {kmeans_silhouette:<18.4f} {kmeans_calinski:<20.2f} {'N/A':<18}")
    print(f"{'PCA+K-Means':<20} {pca_silhouette:<18.4f} {pca_calinski:<20.2f} {pca_results['explained_variance'].sum():<18.4f}")
    
    # Determine which method performs better
    better_silhouette = "PCA+K-Means" if pca_silhouette > kmeans_silhouette else "K-Means"
    better_calinski = "PCA+K-Means" if pca_calinski > kmeans_calinski else "K-Means"
    
    print(f"\n🏆 PERFORMANCE WINNERS:")
    print(f"   Silhouette Score: {better_silhouette} ({max(pca_silhouette, kmeans_silhouette):.4f})")
    print(f"   Calinski-Harabasz: {better_calinski} ({max(pca_calinski, kmeans_calinski):.2f})")
    
    # Create file-to-cluster mapping
    file_cluster_mapping = pd.DataFrame({
        'file_name': raw_data['file_name'],
        'file_id': raw_data['file_id'],
        'kmeans_cluster': kmeans_labels,
        'pca_kmeans_cluster': pca_kmeans_labels
    })
    
    # Group by file to get file-level cluster assignments
    file_assignments = file_cluster_mapping.groupby(['file_name', 'file_id']).agg({
        'kmeans_cluster': lambda x: x.mode().iloc[0],  # Most common cluster for this file
        'pca_kmeans_cluster': lambda x: x.mode().iloc[0]
    }).reset_index()
    
    # Add agreement column
    file_assignments['methods_agree'] = (file_assignments['kmeans_cluster'] == 
                                        file_assignments['pca_kmeans_cluster'])
    
    print(f"\n📁 FILE-TO-CLUSTER ASSIGNMENTS:")
    print(f"   Total files analyzed: {len(file_assignments)}")
    print(f"   Methods agree on: {file_assignments['methods_agree'].sum()} files ({file_assignments['methods_agree'].mean()*100:.1f}%)")
    print(f"   Methods disagree on: {(~file_assignments['methods_agree']).sum()} files ({(~file_assignments['methods_agree']).mean()*100:.1f}%)")
    
    # Show cluster distribution for each method
    print(f"\n📈 CLUSTER DISTRIBUTION BY METHOD:")
    
    kmeans_dist = file_assignments['kmeans_cluster'].value_counts().sort_index()
    pca_dist = file_assignments['pca_kmeans_cluster'].value_counts().sort_index()
    
    print(f"\nK-Means Cluster Distribution:")
    for cluster, count in kmeans_dist.items():
        print(f"   Cluster {cluster}: {count} files ({count/len(file_assignments)*100:.1f}%)")
    
    print(f"\nPCA+K-Means Cluster Distribution:")
    for cluster, count in pca_dist.items():
        print(f"   Cluster {cluster}: {count} files ({count/len(file_assignments)*100:.1f}%)")
    
    # Show detailed file assignments
    print(f"\n📋 DETAILED FILE ASSIGNMENTS:")
    print(f"{'File Name':<15} {'File ID':<8} {'K-Means':<10} {'PCA+K-Means':<12} {'Agree':<8}")
    print("-" * 60)
    
    for _, row in file_assignments.iterrows():
        agree_symbol = "✓" if row['methods_agree'] else "✗"
        print(f"{row['file_name']:<15} {row['file_id']:<8} {row['kmeans_cluster']:<10} {row['pca_kmeans_cluster']:<12} {agree_symbol:<8}")
    
    return file_assignments

# Run the detailed comparison
file_assignments = compare_kmeans_vs_pca_kmeans(
    features, raw_data, 
    kmeans_labels, 
    pca_results['kmeans_labels']
)

def compare_all_methods():
    """Compare all clustering methods"""
    print("\n" + "="*60)
    print("COMPREHENSIVE COMPARISON OF ALL CLUSTERING METHODS")
    print("="*60)
    
    # Compile results
    methods_data = {
        'Method': ['K-Means', 'GMM', 'DBSCAN', 'PCA + K-Means', 'PCA + GMM'],
        'Silhouette Score': [
            kmeans_metrics['silhouette'],
            gmm_metrics['silhouette'],
            dbscan_metrics['silhouette'] if dbscan_metrics['silhouette'] > -1 else 'N/A',
            pca_results['kmeans_silhouette'],
            pca_results['gmm_silhouette']
        ],
        'Calinski-Harabasz': [
            kmeans_metrics['calinski'],
            gmm_metrics['calinski'],
            dbscan_metrics['calinski'] if dbscan_metrics['calinski'] > -1 else 'N/A',
            'N/A',  # Not calculated for PCA methods in this example
            'N/A'
        ],
        'Special Metrics': [
            f"Inertia: {kmeans_metrics['inertia']:.0f}",
            f"AIC: {gmm_metrics['aic']:.0f}, BIC: {gmm_metrics['bic']:.0f}",
            f"Clusters: {dbscan_metrics['n_clusters']}, Noise: {dbscan_metrics['n_noise']}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}"
        ]
    }
    
    # Create comparison DataFrame
    comparison_df = pd.DataFrame(methods_data)
    print("\nMethod Comparison Table:")
    print(comparison_df.to_string(index=False))
    
    # Create comparison visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Comprehensive Method Comparison', fontsize=16, fontweight='bold')
    
    # PCA for consistent visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Plot all methods
    methods = [
        ('K-Means', kmeans_labels),
        ('GMM', gmm_labels),
        ('DBSCAN', dbscan_labels),
        ('PCA + K-Means', pca_results['kmeans_labels']),
        ('PCA + GMM', pca_results['gmm_labels'])
    ]
    
    for i, (method_name, labels) in enumerate(methods):
        row, col = i // 3, i % 3
        if i < 3:  # Original space visualization
            scatter = axes[row, col].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
        else:  # PCA space visualization
            scatter = axes[row, col].scatter(pca_results['pca_features'][:, 0], pca_results['pca_features'][:, 1], 
                                           c=labels, cmap='viridis', alpha=0.6)
        
        axes[row, col].set_title(method_name)
        axes[row, col].set_xlabel('PC1' if i < 3 else 'PC1 (PCA)')
        axes[row, col].set_ylabel('PC2' if i < 3 else 'PC2 (PCA)')
    
    # Silhouette score comparison
    valid_methods = []
    valid_scores = []
    for method, score in zip(methods_data['Method'], methods_data['Silhouette Score']):
        if score != 'N/A':
            valid_methods.append(method)
            valid_scores.append(float(score))
    
    axes[1, 2].bar(valid_methods, valid_scores, color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
    axes[1, 2].set_title('Silhouette Score Comparison')
    axes[1, 2].set_ylabel('Silhouette Score')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    # Add score values on bars
    for i, score in enumerate(valid_scores):
        axes[1, 2].text(i, score + 0.01, f'{score:.3f}', ha='center')
    
    plt.tight_layout()
    plt.show()
    
    # Find best method
    best_method_idx = np.argmax(valid_scores)
    best_method = valid_methods[best_method_idx]
    best_score = valid_scores[best_method_idx]
    
    print(f"\n🏆 BEST METHOD: {best_method}")
    print(f"   Silhouette Score: {best_score:.4f}")
    
    # Recommendations
    print("\n📊 RECOMMENDATIONS:")
    print("   • For interpretability: K-Means (clear centroids, easy to understand)")
    print("   • For probabilistic clustering: GMM (provides cluster probabilities)")
    print("   • For noise detection: DBSCAN (identifies outliers automatically)")
    print("   • For dimensionality reduction: PCA + clustering (reduces complexity)")
    
    return comparison_df

# Run comprehensive comparison
comparison_results = compare_all_methods()

def compare_all_methods():
    """Compare all clustering methods"""
    print("\n" + "="*60)
    print("COMPREHENSIVE COMPARISON OF ALL CLUSTERING METHODS")
    print("="*60)
    
    # Compile results
    methods_data = {
        'Method': ['K-Means', 'GMM', 'DBSCAN', 'PCA + K-Means', 'PCA + GMM'],
        'Silhouette Score': [
            kmeans_metrics['silhouette'],
            gmm_metrics['silhouette'],
            dbscan_metrics['silhouette'] if dbscan_metrics['silhouette'] > -1 else 'N/A',
            pca_results['kmeans_silhouette'],
            pca_results['gmm_silhouette']
        ],
        'Calinski-Harabasz': [
            kmeans_metrics['calinski'],
            gmm_metrics['calinski'],
            dbscan_metrics['calinski'] if dbscan_metrics['calinski'] > -1 else 'N/A',
            'N/A',  # Not calculated for PCA methods in this example
            'N/A'
        ],
        'Special Metrics': [
            f"Inertia: {kmeans_metrics['inertia']:.0f}",
            f"AIC: {gmm_metrics['aic']:.0f}, BIC: {gmm_metrics['bic']:.0f}",
            f"Clusters: {dbscan_metrics['n_clusters']}, Noise: {dbscan_metrics['n_noise']}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}"
        ]
    }
    
    # Create comparison DataFrame
    comparison_df = pd.DataFrame(methods_data)
    print("\nMethod Comparison Table:")
    print(comparison_df.to_string(index=False))
    
    # Create comparison visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Comprehensive Method Comparison', fontsize=16, fontweight='bold')
    
    # PCA for consistent visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Plot all methods
    methods = [
        ('K-Means', kmeans_labels),
        ('GMM', gmm_labels),
        ('DBSCAN', dbscan_labels),
        ('PCA + K-Means', pca_results['kmeans_labels']),
        ('PCA + GMM', pca_results['gmm_labels'])
    ]
    
    for i, (method_name, labels) in enumerate(methods):
        row, col = i // 3, i % 3
        if i < 3:  # Original space visualization
            scatter = axes[row, col].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
        else:  # PCA space visualization
            scatter = axes[row, col].scatter(pca_results['pca_features'][:, 0], pca_results['pca_features'][:, 1], 
                                           c=labels, cmap='viridis', alpha=0.6)
        
        axes[row, col].set_title(method_name)
        axes[row, col].set_xlabel('PC1' if i < 3 else 'PC1 (PCA)')
        axes[row, col].set_ylabel('PC2' if i < 3 else 'PC2 (PCA)')
    
    # Silhouette score comparison
    valid_methods = []
    valid_scores = []
    for method, score in zip(methods_data['Method'], methods_data['Silhouette Score']):
        if score != 'N/A':
            valid_methods.append(method)
            valid_scores.append(float(score))
    
    axes[1, 2].bar(valid_methods, valid_scores, color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
    axes[1, 2].set_title('Silhouette Score Comparison')
    axes[1, 2].set_ylabel('Silhouette Score')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    # Add score values on bars
    for i, score in enumerate(valid_scores):
        axes[1, 2].text(i, score + 0.01, f'{score:.3f}', ha='center')
    
    plt.tight_layout()
    plt.show()
    
    # Find best method
    best_method_idx = np.argmax(valid_scores)
    best_method = valid_methods[best_method_idx]
    best_score = valid_scores[best_method_idx]
    
    print(f"\n🏆 BEST METHOD: {best_method}")
    print(f"   Silhouette Score: {best_score:.4f}")
    
    # Recommendations
    print("\n📊 RECOMMENDATIONS:")
    print("   • For interpretability: K-Means (clear centroids, easy to understand)")
    print("   • For probabilistic clustering: GMM (provides cluster probabilities)")
    print("   • For noise detection: DBSCAN (identifies outliers automatically)")
    print("   • For dimensionality reduction: PCA + clustering (reduces complexity)")
    
    return comparison_df

# Run comprehensive comparison
comparison_results = compare_all_methods()

def compare_all_methods():
    """Compare all clustering methods"""
    print("\n" + "="*60)
    print("COMPREHENSIVE COMPARISON OF ALL CLUSTERING METHODS")
    print("="*60)
    
    # Compile results
    methods_data = {
        'Method': ['K-Means', 'GMM', 'DBSCAN', 'PCA + K-Means', 'PCA + GMM'],
        'Silhouette Score': [
            kmeans_metrics['silhouette'],
            gmm_metrics['silhouette'],
            dbscan_metrics['silhouette'] if dbscan_metrics['silhouette'] > -1 else 'N/A',
            pca_results['kmeans_silhouette'],
            pca_results['gmm_silhouette']
        ],
        'Calinski-Harabasz': [
            kmeans_metrics['calinski'],
            gmm_metrics['calinski'],
            dbscan_metrics['calinski'] if dbscan_metrics['calinski'] > -1 else 'N/A',
            'N/A',  # Not calculated for PCA methods in this example
            'N/A'
        ],
        'Special Metrics': [
            f"Inertia: {kmeans_metrics['inertia']:.0f}",
            f"AIC: {gmm_metrics['aic']:.0f}, BIC: {gmm_metrics['bic']:.0f}",
            f"Clusters: {dbscan_metrics['n_clusters']}, Noise: {dbscan_metrics['n_noise']}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}"
        ]
    }
    
    # Create comparison DataFrame
    comparison_df = pd.DataFrame(methods_data)
    print("\nMethod Comparison Table:")
    print(comparison_df.to_string(index=False))
    
    # Create comparison visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Comprehensive Method Comparison', fontsize=16, fontweight='bold')
    
    # PCA for consistent visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Plot all methods
    methods = [
        ('K-Means', kmeans_labels),
        ('GMM', gmm_labels),
        ('DBSCAN', dbscan_labels),
        ('PCA + K-Means', pca_results['kmeans_labels']),
        ('PCA + GMM', pca_results['gmm_labels'])
    ]
    
    for i, (method_name, labels) in enumerate(methods):
        row, col = i // 3, i % 3
        if i < 3:  # Original space visualization
            scatter = axes[row, col].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
        else:  # PCA space visualization
            scatter = axes[row, col].scatter(pca_results['pca_features'][:, 0], pca_results['pca_features'][:, 1], 
                                           c=labels, cmap='viridis', alpha=0.6)
        
        axes[row, col].set_title(method_name)
        axes[row, col].set_xlabel('PC1' if i < 3 else 'PC1 (PCA)')
        axes[row, col].set_ylabel('PC2' if i < 3 else 'PC2 (PCA)')
    
    # Silhouette score comparison
    valid_methods = []
    valid_scores = []
    for method, score in zip(methods_data['Method'], methods_data['Silhouette Score']):
        if score != 'N/A':
            valid_methods.append(method)
            valid_scores.append(float(score))
    
    axes[1, 2].bar(valid_methods, valid_scores, color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
    axes[1, 2].set_title('Silhouette Score Comparison')
    axes[1, 2].set_ylabel('Silhouette Score')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    # Add score values on bars
    for i, score in enumerate(valid_scores):
        axes[1, 2].text(i, score + 0.01, f'{score:.3f}', ha='center')
    
    plt.tight_layout()
    plt.show()
    
    # Find best method
    best_method_idx = np.argmax(valid_scores)
    best_method = valid_methods[best_method_idx]
    best_score = valid_scores[best_method_idx]
    
    print(f"\n🏆 BEST METHOD: {best_method}")
    print(f"   Silhouette Score: {best_score:.4f}")
    
    # Recommendations
    print("\n📊 RECOMMENDATIONS:")
    print("   • For interpretability: K-Means (clear centroids, easy to understand)")
    print("   • For probabilistic clustering: GMM (provides cluster probabilities)")
    print("   • For noise detection: DBSCAN (identifies outliers automatically)")
    print("   • For dimensionality reduction: PCA + clustering (reduces complexity)")
    
    return comparison_df

# Run comprehensive comparison
comparison_results = compare_all_methods()

def compare_all_methods():
    """Compare all clustering methods"""
    print("\n" + "="*60)
    print("COMPREHENSIVE COMPARISON OF ALL CLUSTERING METHODS")
    print("="*60)
    
    # Compile results
    methods_data = {
        'Method': ['K-Means', 'GMM', 'DBSCAN', 'PCA + K-Means', 'PCA + GMM'],
        'Silhouette Score': [
            kmeans_metrics['silhouette'],
            gmm_metrics['silhouette'],
            dbscan_metrics['silhouette'] if dbscan_metrics['silhouette'] > -1 else 'N/A',
            pca_results['kmeans_silhouette'],
            pca_results['gmm_silhouette']
        ],
        'Calinski-Harabasz': [
            kmeans_metrics['calinski'],
            gmm_metrics['calinski'],
            dbscan_metrics['calinski'] if dbscan_metrics['calinski'] > -1 else 'N/A',
            'N/A',  # Not calculated for PCA methods in this example
            'N/A'
        ],
        'Special Metrics': [
            f"Inertia: {kmeans_metrics['inertia']:.0f}",
            f"AIC: {gmm_metrics['aic']:.0f}, BIC: {gmm_metrics['bic']:.0f}",
            f"Clusters: {dbscan_metrics['n_clusters']}, Noise: {dbscan_metrics['n_noise']}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}"
        ]
    }
    
    # Create comparison DataFrame
    comparison_df = pd.DataFrame(methods_data)
    print("\nMethod Comparison Table:")
    print(comparison_df.to_string(index=False))
    
    # Create comparison visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Comprehensive Method Comparison', fontsize=16, fontweight='bold')
    
    # PCA for consistent visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Plot all methods
    methods = [
        ('K-Means', kmeans_labels),
        ('GMM', gmm_labels),
        ('DBSCAN', dbscan_labels),
        ('PCA + K-Means', pca_results['kmeans_labels']),
        ('PCA + GMM', pca_results['gmm_labels'])
    ]
    
    for i, (method_name, labels) in enumerate(methods):
        row, col = i // 3, i % 3
        if i < 3:  # Original space visualization
            scatter = axes[row, col].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
        else:  # PCA space visualization
            scatter = axes[row, col].scatter(pca_results['pca_features'][:, 0], pca_results['pca_features'][:, 1], 
                                           c=labels, cmap='viridis', alpha=0.6)
        
        axes[row, col].set_title(method_name)
        axes[row, col].set_xlabel('PC1' if i < 3 else 'PC1 (PCA)')
        axes[row, col].set_ylabel('PC2' if i < 3 else 'PC2 (PCA)')
    
    # Silhouette score comparison
    valid_methods = []
    valid_scores = []
    for method, score in zip(methods_data['Method'], methods_data['Silhouette Score']):
        if score != 'N/A':
            valid_methods.append(method)
            valid_scores.append(float(score))
    
    axes[1, 2].bar(valid_methods, valid_scores, color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
    axes[1, 2].set_title('Silhouette Score Comparison')
    axes[1, 2].set_ylabel('Silhouette Score')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    # Add score values on bars
    for i, score in enumerate(valid_scores):
        axes[1, 2].text(i, score + 0.01, f'{score:.3f}', ha='center')
    
    plt.tight_layout()
    plt.show()
    
    # Find best method
    best_method_idx = np.argmax(valid_scores)
    best_method = valid_methods[best_method_idx]
    best_score = valid_scores[best_method_idx]
    
    print(f"\n🏆 BEST METHOD: {best_method}")
    print(f"   Silhouette Score: {best_score:.4f}")
    
    # Recommendations
    print("\n📊 RECOMMENDATIONS:")
    print("   • For interpretability: K-Means (clear centroids, easy to understand)")
    print("   • For probabilistic clustering: GMM (provides cluster probabilities)")
    print("   • For noise detection: DBSCAN (identifies outliers automatically)")
    print("   • For dimensionality reduction: PCA + clustering (reduces complexity)")
    
    return comparison_df

# Run comprehensive comparison
comparison_results = compare_all_methods()

def compare_all_methods():
    """Compare all clustering methods"""
    print("\n" + "="*60)
    print("COMPREHENSIVE COMPARISON OF ALL CLUSTERING METHODS")
    print("="*60)
    
    # Compile results
    methods_data = {
        'Method': ['K-Means', 'GMM', 'DBSCAN', 'PCA + K-Means', 'PCA + GMM'],
        'Silhouette Score': [
            kmeans_metrics['silhouette'],
            gmm_metrics['silhouette'],
            dbscan_metrics['silhouette'] if dbscan_metrics['silhouette'] > -1 else 'N/A',
            pca_results['kmeans_silhouette'],
            pca_results['gmm_silhouette']
        ],
        'Calinski-Harabasz': [
            kmeans_metrics['calinski'],
            gmm_metrics['calinski'],
            dbscan_metrics['calinski'] if dbscan_metrics['calinski'] > -1 else 'N/A',
            'N/A',  # Not calculated for PCA methods in this example
            'N/A'
        ],
        'Special Metrics': [
            f"Inertia: {kmeans_metrics['inertia']:.0f}",
            f"AIC: {gmm_metrics['aic']:.0f}, BIC: {gmm_metrics['bic']:.0f}",
            f"Clusters: {dbscan_metrics['n_clusters']}, Noise: {dbscan_metrics['n_noise']}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}"
        ]
    }
    
    # Create comparison DataFrame
    comparison_df = pd.DataFrame(methods_data)
    print("\nMethod Comparison Table:")
    print(comparison_df.to_string(index=False))
    
    # Create comparison visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Comprehensive Method Comparison', fontsize=16, fontweight='bold')
    
    # PCA for consistent visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Plot all methods
    methods = [
        ('K-Means', kmeans_labels),
        ('GMM', gmm_labels),
        ('DBSCAN', dbscan_labels),
        ('PCA + K-Means', pca_results['kmeans_labels']),
        ('PCA + GMM', pca_results['gmm_labels'])
    ]
    
    for i, (method_name, labels) in enumerate(methods):
        row, col = i // 3, i % 3
        if i < 3:  # Original space visualization
            scatter = axes[row, col].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
        else:  # PCA space visualization
            scatter = axes[row, col].scatter(pca_results['pca_features'][:, 0], pca_results['pca_features'][:, 1], 
                                           c=labels, cmap='viridis', alpha=0.6)
        
        axes[row, col].set_title(method_name)
        axes[row, col].set_xlabel('PC1' if i < 3 else 'PC1 (PCA)')
        axes[row, col].set_ylabel('PC2' if i < 3 else 'PC2 (PCA)')
    
    # Silhouette score comparison
    valid_methods = []
    valid_scores = []
    for method, score in zip(methods_data['Method'], methods_data['Silhouette Score']):
        if score != 'N/A':
            valid_methods.append(method)
            valid_scores.append(float(score))
    
    axes[1, 2].bar(valid_methods, valid_scores, color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
    axes[1, 2].set_title('Silhouette Score Comparison')
    axes[1, 2].set_ylabel('Silhouette Score')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    # Add score values on bars
    for i, score in enumerate(valid_scores):
        axes[1, 2].text(i, score + 0.01, f'{score:.3f}', ha='center')
    
    plt.tight_layout()
    plt.show()
    
    # Find best method
    best_method_idx = np.argmax(valid_scores)
    best_method = valid_methods[best_method_idx]
    best_score = valid_scores[best_method_idx]
    
    print(f"\n🏆 BEST METHOD: {best_method}")
    print(f"   Silhouette Score: {best_score:.4f}")
    
    # Recommendations
    print("\n📊 RECOMMENDATIONS:")
    print("   • For interpretability: K-Means (clear centroids, easy to understand)")
    print("   • For probabilistic clustering: GMM (provides cluster probabilities)")
    print("   • For noise detection: DBSCAN (identifies outliers automatically)")
    print("   • For dimensionality reduction: PCA + clustering (reduces complexity)")
    
    return comparison_df

# Run comprehensive comparison
comparison_results = compare_all_methods()

def compare_all_methods():
    """Compare all clustering methods"""
    print("\n" + "="*60)
    print("COMPREHENSIVE COMPARISON OF ALL CLUSTERING METHODS")
    print("="*60)
    
    # Compile results
    methods_data = {
        'Method': ['K-Means', 'GMM', 'DBSCAN', 'PCA + K-Means', 'PCA + GMM'],
        'Silhouette Score': [
            kmeans_metrics['silhouette'],
            gmm_metrics['silhouette'],
            dbscan_metrics['silhouette'] if dbscan_metrics['silhouette'] > -1 else 'N/A',
            pca_results['kmeans_silhouette'],
            pca_results['gmm_silhouette']
        ],
        'Calinski-Harabasz': [
            kmeans_metrics['calinski'],
            gmm_metrics['calinski'],
            dbscan_metrics['calinski'] if dbscan_metrics['calinski'] > -1 else 'N/A',
            'N/A',  # Not calculated for PCA methods in this example
            'N/A'
        ],
        'Special Metrics': [
            f"Inertia: {kmeans_metrics['inertia']:.0f}",
            f"AIC: {gmm_metrics['aic']:.0f}, BIC: {gmm_metrics['bic']:.0f}",
            f"Clusters: {dbscan_metrics['n_clusters']}, Noise: {dbscan_metrics['n_noise']}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}"
        ]
    }
    
    # Create comparison DataFrame
    comparison_df = pd.DataFrame(methods_data)
    print("\nMethod Comparison Table:")
    print(comparison_df.to_string(index=False))
    
    # Create comparison visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Comprehensive Method Comparison', fontsize=16, fontweight='bold')
    
    # PCA for consistent visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Plot all methods
    methods = [
        ('K-Means', kmeans_labels),
        ('GMM', gmm_labels),
        ('DBSCAN', dbscan_labels),
        ('PCA + K-Means', pca_results['kmeans_labels']),
        ('PCA + GMM', pca_results['gmm_labels'])
    ]
    
    for i, (method_name, labels) in enumerate(methods):
        row, col = i // 3, i % 3
        if i < 3:  # Original space visualization
            scatter = axes[row, col].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
        else:  # PCA space visualization
            scatter = axes[row, col].scatter(pca_results['pca_features'][:, 0], pca_results['pca_features'][:, 1], 
                                           c=labels, cmap='viridis', alpha=0.6)
        
        axes[row, col].set_title(method_name)
        axes[row, col].set_xlabel('PC1' if i < 3 else 'PC1 (PCA)')
        axes[row, col].set_ylabel('PC2' if i < 3 else 'PC2 (PCA)')
    
    # Silhouette score comparison
    valid_methods = []
    valid_scores = []
    for method, score in zip(methods_data['Method'], methods_data['Silhouette Score']):
        if score != 'N/A':
            valid_methods.append(method)
            valid_scores.append(float(score))
    
    axes[1, 2].bar(valid_methods, valid_scores, color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
    axes[1, 2].set_title('Silhouette Score Comparison')
    axes[1, 2].set_ylabel('Silhouette Score')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    # Add score values on bars
    for i, score in enumerate(valid_scores):
        axes[1, 2].text(i, score + 0.01, f'{score:.3f}', ha='center')
    
    plt.tight_layout()
    plt.show()
    
    # Find best method
    best_method_idx = np.argmax(valid_scores)
    best_method = valid_methods[best_method_idx]
    best_score = valid_scores[best_method_idx]
    
    print(f"\n🏆 BEST METHOD: {best_method}")
    print(f"   Silhouette Score: {best_score:.4f}")
    
    # Recommendations
    print("\n📊 RECOMMENDATIONS:")
    print("   • For interpretability: K-Means (clear centroids, easy to understand)")
    print("   • For probabilistic clustering: GMM (provides cluster probabilities)")
    print("   • For noise detection: DBSCAN (identifies outliers automatically)")
    print("   • For dimensionality reduction: PCA + clustering (reduces complexity)")
    
    return comparison_df

# Run comprehensive comparison
comparison_results = compare_all_methods()

def compare_all_methods():
    """Compare all clustering methods"""
    print("\n" + "="*60)
    print("COMPREHENSIVE COMPARISON OF ALL CLUSTERING METHODS")
    print("="*60)
    
    # Compile results
    methods_data = {
        'Method': ['K-Means', 'GMM', 'DBSCAN', 'PCA + K-Means', 'PCA + GMM'],
        'Silhouette Score': [
            kmeans_metrics['silhouette'],
            gmm_metrics['silhouette'],
            dbscan_metrics['silhouette'] if dbscan_metrics['silhouette'] > -1 else 'N/A',
            pca_results['kmeans_silhouette'],
            pca_results['gmm_silhouette']
        ],
        'Calinski-Harabasz': [
            kmeans_metrics['calinski'],
            gmm_metrics['calinski'],
            dbscan_metrics['calinski'] if dbscan_metrics['calinski'] > -1 else 'N/A',
            'N/A',  # Not calculated for PCA methods in this example
            'N/A'
        ],
        'Special Metrics': [
            f"Inertia: {kmeans_metrics['inertia']:.0f}",
            f"AIC: {gmm_metrics['aic']:.0f}, BIC: {gmm_metrics['bic']:.0f}",
            f"Clusters: {dbscan_metrics['n_clusters']}, Noise: {dbscan_metrics['n_noise']}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}"
        ]
    }
    
    # Create comparison DataFrame
    comparison_df = pd.DataFrame(methods_data)
    print("\nMethod Comparison Table:")
    print(comparison_df.to_string(index=False))
    
    # Create comparison visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Comprehensive Method Comparison', fontsize=16, fontweight='bold')
    
    # PCA for consistent visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Plot all methods
    methods = [
        ('K-Means', kmeans_labels),
        ('GMM', gmm_labels),
        ('DBSCAN', dbscan_labels),
        ('PCA + K-Means', pca_results['kmeans_labels']),
        ('PCA + GMM', pca_results['gmm_labels'])
    ]
    
    for i, (method_name, labels) in enumerate(methods):
        row, col = i // 3, i % 3
        if i < 3:  # Original space visualization
            scatter = axes[row, col].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
        else:  # PCA space visualization
            scatter = axes[row, col].scatter(pca_results['pca_features'][:, 0], pca_results['pca_features'][:, 1], 
                                           c=labels, cmap='viridis', alpha=0.6)
        
        axes[row, col].set_title(method_name)
        axes[row, col].set_xlabel('PC1' if i < 3 else 'PC1 (PCA)')
        axes[row, col].set_ylabel('PC2' if i < 3 else 'PC2 (PCA)')
    
    # Silhouette score comparison
    valid_methods = []
    valid_scores = []
    for method, score in zip(methods_data['Method'], methods_data['Silhouette Score']):
        if score != 'N/A':
            valid_methods.append(method)
            valid_scores.append(float(score))
    
    axes[1, 2].bar(valid_methods, valid_scores, color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
    axes[1, 2].set_title('Silhouette Score Comparison')
    axes[1, 2].set_ylabel('Silhouette Score')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    # Add score values on bars
    for i, score in enumerate(valid_scores):
        axes[1, 2].text(i, score + 0.01, f'{score:.3f}', ha='center')
    
    plt.tight_layout()
    plt.show()
    
    # Find best method
    best_method_idx = np.argmax(valid_scores)
    best_method = valid_methods[best_method_idx]
    best_score = valid_scores[best_method_idx]
    
    print(f"\n🏆 BEST METHOD: {best_method}")
    print(f"   Silhouette Score: {best_score:.4f}")
    
    # Recommendations
    print("\n📊 RECOMMENDATIONS:")
    print("   • For interpretability: K-Means (clear centroids, easy to understand)")
    print("   • For probabilistic clustering: GMM (provides cluster probabilities)")
    print("   • For noise detection: DBSCAN (identifies outliers automatically)")
    print("   • For dimensionality reduction: PCA + clustering (reduces complexity)")
    
    return comparison_df

# Run comprehensive comparison
comparison_results = compare_all_methods()

def compare_all_methods():
    """Compare all clustering methods"""
    print("\n" + "="*60)
    print("COMPREHENSIVE COMPARISON OF ALL CLUSTERING METHODS")
    print("="*60)
    
    # Compile results
    methods_data = {
        'Method': ['K-Means', 'GMM', 'DBSCAN', 'PCA + K-Means', 'PCA + GMM'],
        'Silhouette Score': [
            kmeans_metrics['silhouette'],
            gmm_metrics['silhouette'],
            dbscan_metrics['silhouette'] if dbscan_metrics['silhouette'] > -1 else 'N/A',
            pca_results['kmeans_silhouette'],
            pca_results['gmm_silhouette']
        ],
        'Calinski-Harabasz': [
            kmeans_metrics['calinski'],
            gmm_metrics['calinski'],
            dbscan_metrics['calinski'] if dbscan_metrics['calinski'] > -1 else 'N/A',
            'N/A',  # Not calculated for PCA methods in this example
            'N/A'
        ],
        'Special Metrics': [
            f"Inertia: {kmeans_metrics['inertia']:.0f}",
            f"AIC: {gmm_metrics['aic']:.0f}, BIC: {gmm_metrics['bic']:.0f}",
            f"Clusters: {dbscan_metrics['n_clusters']}, Noise: {dbscan_metrics['n_noise']}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}"
        ]
    }
    
    # Create comparison DataFrame
    comparison_df = pd.DataFrame(methods_data)
    print("\nMethod Comparison Table:")
    print(comparison_df.to_string(index=False))
    
    # Create comparison visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Comprehensive Method Comparison', fontsize=16, fontweight='bold')
    
    # PCA for consistent visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Plot all methods
    methods = [
        ('K-Means', kmeans_labels),
        ('GMM', gmm_labels),
        ('DBSCAN', dbscan_labels),
        ('PCA + K-Means', pca_results['kmeans_labels']),
        ('PCA + GMM', pca_results['gmm_labels'])
    ]
    
    for i, (method_name, labels) in enumerate(methods):
        row, col = i // 3, i % 3
        if i < 3:  # Original space visualization
            scatter = axes[row, col].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
        else:  # PCA space visualization
            scatter = axes[row, col].scatter(pca_results['pca_features'][:, 0], pca_results['pca_features'][:, 1], 
                                           c=labels, cmap='viridis', alpha=0.6)
        
        axes[row, col].set_title(method_name)
        axes[row, col].set_xlabel('PC1' if i < 3 else 'PC1 (PCA)')
        axes[row, col].set_ylabel('PC2' if i < 3 else 'PC2 (PCA)')
    
    # Silhouette score comparison
    valid_methods = []
    valid_scores = []
    for method, score in zip(methods_data['Method'], methods_data['Silhouette Score']):
        if score != 'N/A':
            valid_methods.append(method)
            valid_scores.append(float(score))
    
    axes[1, 2].bar(valid_methods, valid_scores, color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
    axes[1, 2].set_title('Silhouette Score Comparison')
    axes[1, 2].set_ylabel('Silhouette Score')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    # Add score values on bars
    for i, score in enumerate(valid_scores):
        axes[1, 2].text(i, score + 0.01, f'{score:.3f}', ha='center')
    
    plt.tight_layout()
    plt.show()
    
    # Find best method
    best_method_idx = np.argmax(valid_scores)
    best_method = valid_methods[best_method_idx]
    best_score = valid_scores[best_method_idx]
    
    print(f"\n🏆 BEST METHOD: {best_method}")
    print(f"   Silhouette Score: {best_score:.4f}")
    
    # Recommendations
    print("\n📊 RECOMMENDATIONS:")
    print("   • For interpretability: K-Means (clear centroids, easy to understand)")
    print("   • For probabilistic clustering: GMM (provides cluster probabilities)")
    print("   • For noise detection: DBSCAN (identifies outliers automatically)")
    print("   • For dimensionality reduction: PCA + clustering (reduces complexity)")
    
    return comparison_df

# Run comprehensive comparison
comparison_results = compare_all_methods()

def compare_all_methods():
    """Compare all clustering methods"""
    print("\n" + "="*60)
    print("COMPREHENSIVE COMPARISON OF ALL CLUSTERING METHODS")
    print("="*60)
    
    # Compile results
    methods_data = {
        'Method': ['K-Means', 'GMM', 'DBSCAN', 'PCA + K-Means', 'PCA + GMM'],
        'Silhouette Score': [
            kmeans_metrics['silhouette'],
            gmm_metrics['silhouette'],
            dbscan_metrics['silhouette'] if dbscan_metrics['silhouette'] > -1 else 'N/A',
            pca_results['kmeans_silhouette'],
            pca_results['gmm_silhouette']
        ],
        'Calinski-Harabasz': [
            kmeans_metrics['calinski'],
            gmm_metrics['calinski'],
            dbscan_metrics['calinski'] if dbscan_metrics['calinski'] > -1 else 'N/A',
            'N/A',  # Not calculated for PCA methods in this example
            'N/A'
        ],
        'Special Metrics': [
            f"Inertia: {kmeans_metrics['inertia']:.0f}",
            f"AIC: {gmm_metrics['aic']:.0f}, BIC: {gmm_metrics['bic']:.0f}",
            f"Clusters: {dbscan_metrics['n_clusters']}, Noise: {dbscan_metrics['n_noise']}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}"
        ]
    }
    
    # Create comparison DataFrame
    comparison_df = pd.DataFrame(methods_data)
    print("\nMethod Comparison Table:")
    print(comparison_df.to_string(index=False))
    
    # Create comparison visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Comprehensive Method Comparison', fontsize=16, fontweight='bold')
    
    # PCA for consistent visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Plot all methods
    methods = [
        ('K-Means', kmeans_labels),
        ('GMM', gmm_labels),
        ('DBSCAN', dbscan_labels),
        ('PCA + K-Means', pca_results['kmeans_labels']),
        ('PCA + GMM', pca_results['gmm_labels'])
    ]
    
    for i, (method_name, labels) in enumerate(methods):
        row, col = i // 3, i % 3
        if i < 3:  # Original space visualization
            scatter = axes[row, col].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
        else:  # PCA space visualization
            scatter = axes[row, col].scatter(pca_results['pca_features'][:, 0], pca_results['pca_features'][:, 1], 
                                           c=labels, cmap='viridis', alpha=0.6)
        
        axes[row, col].set_title(method_name)
        axes[row, col].set_xlabel('PC1' if i < 3 else 'PC1 (PCA)')
        axes[row, col].set_ylabel('PC2' if i < 3 else 'PC2 (PCA)')
    
    # Silhouette score comparison
    valid_methods = []
    valid_scores = []
    for method, score in zip(methods_data['Method'], methods_data['Silhouette Score']):
        if score != 'N/A':
            valid_methods.append(method)
            valid_scores.append(float(score))
    
    axes[1, 2].bar(valid_methods, valid_scores, color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
    axes[1, 2].set_title('Silhouette Score Comparison')
    axes[1, 2].set_ylabel('Silhouette Score')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    # Add score values on bars
    for i, score in enumerate(valid_scores):
        axes[1, 2].text(i, score + 0.01, f'{score:.3f}', ha='center')
    
    plt.tight_layout()
    plt.show()
    
    # Find best method
    best_method_idx = np.argmax(valid_scores)
    best_method = valid_methods[best_method_idx]
    best_score = valid_scores[best_method_idx]
    
    print(f"\n🏆 BEST METHOD: {best_method}")
    print(f"   Silhouette Score: {best_score:.4f}")
    
    # Recommendations
    print("\n📊 RECOMMENDATIONS:")
    print("   • For interpretability: K-Means (clear centroids, easy to understand)")
    print("   • For probabilistic clustering: GMM (provides cluster probabilities)")
    print("   • For noise detection: DBSCAN (identifies outliers automatically)")
    print("   • For dimensionality reduction: PCA + clustering (reduces complexity)")
    
    return comparison_df

# Run comprehensive comparison
comparison_results = compare_all_methods()

def compare_all_methods():
    """Compare all clustering methods"""
    print("\n" + "="*60)
    print("COMPREHENSIVE COMPARISON OF ALL CLUSTERING METHODS")
    print("="*60)
    
    # Compile results
    methods_data = {
        'Method': ['K-Means', 'GMM', 'DBSCAN', 'PCA + K-Means', 'PCA + GMM'],
        'Silhouette Score': [
            kmeans_metrics['silhouette'],
            gmm_metrics['silhouette'],
            dbscan_metrics['silhouette'] if dbscan_metrics['silhouette'] > -1 else 'N/A',
            pca_results['kmeans_silhouette'],
            pca_results['gmm_silhouette']
        ],
        'Calinski-Harabasz': [
            kmeans_metrics['calinski'],
            gmm_metrics['calinski'],
            dbscan_metrics['calinski'] if dbscan_metrics['calinski'] > -1 else 'N/A',
            'N/A',  # Not calculated for PCA methods in this example
            'N/A'
        ],
        'Special Metrics': [
            f"Inertia: {kmeans_metrics['inertia']:.0f}",
            f"AIC: {gmm_metrics['aic']:.0f}, BIC: {gmm_metrics['bic']:.0f}",
            f"Clusters: {dbscan_metrics['n_clusters']}, Noise: {dbscan_metrics['n_noise']}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}"
        ]
    }
    
    # Create comparison DataFrame
    comparison_df = pd.DataFrame(methods_data)
    print("\nMethod Comparison Table:")
    print(comparison_df.to_string(index=False))
    
    # Create comparison visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Comprehensive Method Comparison', fontsize=16, fontweight='bold')
    
    # PCA for consistent visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Plot all methods
    methods = [
        ('K-Means', kmeans_labels),
        ('GMM', gmm_labels),
        ('DBSCAN', dbscan_labels),
        ('PCA + K-Means', pca_results['kmeans_labels']),
        ('PCA + GMM', pca_results['gmm_labels'])
    ]
    
    for i, (method_name, labels) in enumerate(methods):
        row, col = i // 3, i % 3
        if i < 3:  # Original space visualization
            scatter = axes[row, col].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
        else:  # PCA space visualization
            scatter = axes[row, col].scatter(pca_results['pca_features'][:, 0], pca_results['pca_features'][:, 1], 
                                           c=labels, cmap='viridis', alpha=0.6)
        
        axes[row, col].set_title(method_name)
        axes[row, col].set_xlabel('PC1' if i < 3 else 'PC1 (PCA)')
        axes[row, col].set_ylabel('PC2' if i < 3 else 'PC2 (PCA)')
    
    # Silhouette score comparison
    valid_methods = []
    valid_scores = []
    for method, score in zip(methods_data['Method'], methods_data['Silhouette Score']):
        if score != 'N/A':
            valid_methods.append(method)
            valid_scores.append(float(score))
    
    axes[1, 2].bar(valid_methods, valid_scores, color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
    axes[1, 2].set_title('Silhouette Score Comparison')
    axes[1, 2].set_ylabel('Silhouette Score')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    # Add score values on bars
    for i, score in enumerate(valid_scores):
        axes[1, 2].text(i, score + 0.01, f'{score:.3f}', ha='center')
    
    plt.tight_layout()
    plt.show()
    
    # Find best method
    best_method_idx = np.argmax(valid_scores)
    best_method = valid_methods[best_method_idx]
    best_score = valid_scores[best_method_idx]
    
    print(f"\n🏆 BEST METHOD: {best_method}")
    print(f"   Silhouette Score: {best_score:.4f}")
    
    # Recommendations
    print("\n📊 RECOMMENDATIONS:")
    print("   • For interpretability: K-Means (clear centroids, easy to understand)")
    print("   • For probabilistic clustering: GMM (provides cluster probabilities)")
    print("   • For noise detection: DBSCAN (identifies outliers automatically)")
    print("   • For dimensionality reduction: PCA + clustering (reduces complexity)")
    
    return comparison_df

# Run comprehensive comparison
comparison_results = compare_all_methods()

def compare_all_methods():
    """Compare all clustering methods"""
    print("\n" + "="*60)
    print("COMPREHENSIVE COMPARISON OF ALL CLUSTERING METHODS")
    print("="*60)
    
    # Compile results
    methods_data = {
        'Method': ['K-Means', 'GMM', 'DBSCAN', 'PCA + K-Means', 'PCA + GMM'],
        'Silhouette Score': [
            kmeans_metrics['silhouette'],
            gmm_metrics['silhouette'],
            dbscan_metrics['silhouette'] if dbscan_metrics['silhouette'] > -1 else 'N/A',
            pca_results['kmeans_silhouette'],
            pca_results['gmm_silhouette']
        ],
        'Calinski-Harabasz': [
            kmeans_metrics['calinski'],
            gmm_metrics['calinski'],
            dbscan_metrics['calinski'] if dbscan_metrics['calinski'] > -1 else 'N/A',
            'N/A',  # Not calculated for PCA methods in this example
            'N/A'
        ],
        'Special Metrics': [
            f"Inertia: {kmeans_metrics['inertia']:.0f}",
            f"AIC: {gmm_metrics['aic']:.0f}, BIC: {gmm_metrics['bic']:.0f}",
            f"Clusters: {dbscan_metrics['n_clusters']}, Noise: {dbscan_metrics['n_noise']}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}"
        ]
    }
    
    # Create comparison DataFrame
    comparison_df = pd.DataFrame(methods_data)
    print("\nMethod Comparison Table:")
    print(comparison_df.to_string(index=False))
    
    # Create comparison visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Comprehensive Method Comparison', fontsize=16, fontweight='bold')
    
    # PCA for consistent visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Plot all methods
    methods = [
        ('K-Means', kmeans_labels),
        ('GMM', gmm_labels),
        ('DBSCAN', dbscan_labels),
        ('PCA + K-Means', pca_results['kmeans_labels']),
        ('PCA + GMM', pca_results['gmm_labels'])
    ]
    
    for i, (method_name, labels) in enumerate(methods):
        row, col = i // 3, i % 3
        if i < 3:  # Original space visualization
            scatter = axes[row, col].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
        else:  # PCA space visualization
            scatter = axes[row, col].scatter(pca_results['pca_features'][:, 0], pca_results['pca_features'][:, 1], 
                                           c=labels, cmap='viridis', alpha=0.6)
        
        axes[row, col].set_title(method_name)
        axes[row, col].set_xlabel('PC1' if i < 3 else 'PC1 (PCA)')
        axes[row, col].set_ylabel('PC2' if i < 3 else 'PC2 (PCA)')
    
    # Silhouette score comparison
    valid_methods = []
    valid_scores = []
    for method, score in zip(methods_data['Method'], methods_data['Silhouette Score']):
        if score != 'N/A':
            valid_methods.append(method)
            valid_scores.append(float(score))
    
    axes[1, 2].bar(valid_methods, valid_scores, color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
    axes[1, 2].set_title('Silhouette Score Comparison')
    axes[1, 2].set_ylabel('Silhouette Score')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    # Add score values on bars
    for i, score in enumerate(valid_scores):
        axes[1, 2].text(i, score + 0.01, f'{score:.3f}', ha='center')
    
    plt.tight_layout()
    plt.show()
    
    # Find best method
    best_method_idx = np.argmax(valid_scores)
    best_method = valid_methods[best_method_idx]
    best_score = valid_scores[best_method_idx]
    
    print(f"\n🏆 BEST METHOD: {best_method}")
    print(f"   Silhouette Score: {best_score:.4f}")
    
    # Recommendations
    print("\n📊 RECOMMENDATIONS:")
    print("   • For interpretability: K-Means (clear centroids, easy to understand)")
    print("   • For probabilistic clustering: GMM (provides cluster probabilities)")
    print("   • For noise detection: DBSCAN (identifies outliers automatically)")
    print("   • For dimensionality reduction: PCA + clustering (reduces complexity)")
    
    return comparison_df

# Run comprehensive comparison
comparison_results = compare_all_methods()

def compare_all_methods():
    """Compare all clustering methods"""
    print("\n" + "="*60)
    print("COMPREHENSIVE COMPARISON OF ALL CLUSTERING METHODS")
    print("="*60)
    
    # Compile results
    methods_data = {
        'Method': ['K-Means', 'GMM', 'DBSCAN', 'PCA + K-Means', 'PCA + GMM'],
        'Silhouette Score': [
            kmeans_metrics['silhouette'],
            gmm_metrics['silhouette'],
            dbscan_metrics['silhouette'] if dbscan_metrics['silhouette'] > -1 else 'N/A',
            pca_results['kmeans_silhouette'],
            pca_results['gmm_silhouette']
        ],
        'Calinski-Harabasz': [
            kmeans_metrics['calinski'],
            gmm_metrics['calinski'],
            dbscan_metrics['calinski'] if dbscan_metrics['calinski'] > -1 else 'N/A',
            'N/A',  # Not calculated for PCA methods in this example
            'N/A'
        ],
        'Special Metrics': [
            f"Inertia: {kmeans_metrics['inertia']:.0f}",
            f"AIC: {gmm_metrics['aic']:.0f}, BIC: {gmm_metrics['bic']:.0f}",
            f"Clusters: {dbscan_metrics['n_clusters']}, Noise: {dbscan_metrics['n_noise']}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}"
        ]
    }
    
    # Create comparison DataFrame
    comparison_df = pd.DataFrame(methods_data)
    print("\nMethod Comparison Table:")
    print(comparison_df.to_string(index=False))
    
    # Create comparison visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Comprehensive Method Comparison', fontsize=16, fontweight='bold')
    
    # PCA for consistent visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Plot all methods
    methods = [
        ('K-Means', kmeans_labels),
        ('GMM', gmm_labels),
        ('DBSCAN', dbscan_labels),
        ('PCA + K-Means', pca_results['kmeans_labels']),
        ('PCA + GMM', pca_results['gmm_labels'])
    ]
    
    for i, (method_name, labels) in enumerate(methods):
        row, col = i // 3, i % 3
        if i < 3:  # Original space visualization
            scatter = axes[row, col].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
        else:  # PCA space visualization
            scatter = axes[row, col].scatter(pca_results['pca_features'][:, 0], pca_results['pca_features'][:, 1], 
                                           c=labels, cmap='viridis', alpha=0.6)
        
        axes[row, col].set_title(method_name)
        axes[row, col].set_xlabel('PC1' if i < 3 else 'PC1 (PCA)')
        axes[row, col].set_ylabel('PC2' if i < 3 else 'PC2 (PCA)')
    
    # Silhouette score comparison
    valid_methods = []
    valid_scores = []
    for method, score in zip(methods_data['Method'], methods_data['Silhouette Score']):
        if score != 'N/A':
            valid_methods.append(method)
            valid_scores.append(float(score))
    
    axes[1, 2].bar(valid_methods, valid_scores, color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
    axes[1, 2].set_title('Silhouette Score Comparison')
    axes[1, 2].set_ylabel('Silhouette Score')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    # Add score values on bars
    for i, score in enumerate(valid_scores):
        axes[1, 2].text(i, score + 0.01, f'{score:.3f}', ha='center')
    
    plt.tight_layout()
    plt.show()
    
    # Find best method
    best_method_idx = np.argmax(valid_scores)
    best_method = valid_methods[best_method_idx]
    best_score = valid_scores[best_method_idx]
    
    print(f"\n🏆 BEST METHOD: {best_method}")
    print(f"   Silhouette Score: {best_score:.4f}")
    
    # Recommendations
    print("\n📊 RECOMMENDATIONS:")
    print("   • For interpretability: K-Means (clear centroids, easy to understand)")
    print("   • For probabilistic clustering: GMM (provides cluster probabilities)")
    print("   • For noise detection: DBSCAN (identifies outliers automatically)")
    print("   • For dimensionality reduction: PCA + clustering (reduces complexity)")
    
    return comparison_df

# Run comprehensive comparison
comparison_results = compare_all_methods()

def compare_all_methods():
    """Compare all clustering methods"""
    print("\n" + "="*60)
    print("COMPREHENSIVE COMPARISON OF ALL CLUSTERING METHODS")
    print("="*60)
    
    # Compile results
    methods_data = {
        'Method': ['K-Means', 'GMM', 'DBSCAN', 'PCA + K-Means', 'PCA + GMM'],
        'Silhouette Score': [
            kmeans_metrics['silhouette'],
            gmm_metrics['silhouette'],
            dbscan_metrics['silhouette'] if dbscan_metrics['silhouette'] > -1 else 'N/A',
            pca_results['kmeans_silhouette'],
            pca_results['gmm_silhouette']
        ],
        'Calinski-Harabasz': [
            kmeans_metrics['calinski'],
            gmm_metrics['calinski'],
            dbscan_metrics['calinski'] if dbscan_metrics['calinski'] > -1 else 'N/A',
            'N/A',  # Not calculated for PCA methods in this example
            'N/A'
        ],
        'Special Metrics': [
            f"Inertia: {kmeans_metrics['inertia']:.0f}",
            f"AIC: {gmm_metrics['aic']:.0f}, BIC: {gmm_metrics['bic']:.0f}",
            f"Clusters: {dbscan_metrics['n_clusters']}, Noise: {dbscan_metrics['n_noise']}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}"
        ]
    }
    
    # Create comparison DataFrame
    comparison_df = pd.DataFrame(methods_data)
    print("\nMethod Comparison Table:")
    print(comparison_df.to_string(index=False))
    
    # Create comparison visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Comprehensive Method Comparison', fontsize=16, fontweight='bold')
    
    # PCA for consistent visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Plot all methods
    methods = [
        ('K-Means', kmeans_labels),
        ('GMM', gmm_labels),
        ('DBSCAN', dbscan_labels),
        ('PCA + K-Means', pca_results['kmeans_labels']),
        ('PCA + GMM', pca_results['gmm_labels'])
    ]
    
    for i, (method_name, labels) in enumerate(methods):
        row, col = i // 3, i % 3
        if i < 3:  # Original space visualization
            scatter = axes[row, col].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
        else:  # PCA space visualization
            scatter = axes[row, col].scatter(pca_results['pca_features'][:, 0], pca_results['pca_features'][:, 1], 
                                           c=labels, cmap='viridis', alpha=0.6)
        
        axes[row, col].set_title(method_name)
        axes[row, col].set_xlabel('PC1' if i < 3 else 'PC1 (PCA)')
        axes[row, col].set_ylabel('PC2' if i < 3 else 'PC2 (PCA)')
    
    # Silhouette score comparison
    valid_methods = []
    valid_scores = []
    for method, score in zip(methods_data['Method'], methods_data['Silhouette Score']):
        if score != 'N/A':
            valid_methods.append(method)
            valid_scores.append(float(score))
    
    axes[1, 2].bar(valid_methods, valid_scores, color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
    axes[1, 2].set_title('Silhouette Score Comparison')
    axes[1, 2].set_ylabel('Silhouette Score')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    # Add score values on bars
    for i, score in enumerate(valid_scores):
        axes[1, 2].text(i, score + 0.01, f'{score:.3f}', ha='center')
    
    plt.tight_layout()
    plt.show()
    
    # Find best method
    best_method_idx = np.argmax(valid_scores)
    best_method = valid_methods[best_method_idx]
    best_score = valid_scores[best_method_idx]
    
    print(f"\n🏆 BEST METHOD: {best_method}")
    print(f"   Silhouette Score: {best_score:.4f}")
    
    # Recommendations
    print("\n📊 RECOMMENDATIONS:")
    print("   • For interpretability: K-Means (clear centroids, easy to understand)")
    print("   • For probabilistic clustering: GMM (provides cluster probabilities)")
    print("   • For noise detection: DBSCAN (identifies outliers automatically)")
    print("   • For dimensionality reduction: PCA + clustering (reduces complexity)")
    
    return comparison_df

# Run comprehensive comparison
comparison_results = compare_all_methods()

def compare_all_methods():
    """Compare all clustering methods"""
    print("\n" + "="*60)
    print("COMPREHENSIVE COMPARISON OF ALL CLUSTERING METHODS")
    print("="*60)
    
    # Compile results
    methods_data = {
        'Method': ['K-Means', 'GMM', 'DBSCAN', 'PCA + K-Means', 'PCA + GMM'],
        'Silhouette Score': [
            kmeans_metrics['silhouette'],
            gmm_metrics['silhouette'],
            dbscan_metrics['silhouette'] if dbscan_metrics['silhouette'] > -1 else 'N/A',
            pca_results['kmeans_silhouette'],
            pca_results['gmm_silhouette']
        ],
        'Calinski-Harabasz': [
            kmeans_metrics['calinski'],
            gmm_metrics['calinski'],
            dbscan_metrics['calinski'] if dbscan_metrics['calinski'] > -1 else 'N/A',
            'N/A',  # Not calculated for PCA methods in this example
            'N/A'
        ],
        'Special Metrics': [
            f"Inertia: {kmeans_metrics['inertia']:.0f}",
            f"AIC: {gmm_metrics['aic']:.0f}, BIC: {gmm_metrics['bic']:.0f}",
            f"Clusters: {dbscan_metrics['n_clusters']}, Noise: {dbscan_metrics['n_noise']}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}"
        ]
    }
    
    # Create comparison DataFrame
    comparison_df = pd.DataFrame(methods_data)
    print("\nMethod Comparison Table:")
    print(comparison_df.to_string(index=False))
    
    # Create comparison visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Comprehensive Method Comparison', fontsize=16, fontweight='bold')
    
    # PCA for consistent visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Plot all methods
    methods = [
        ('K-Means', kmeans_labels),
        ('GMM', gmm_labels),
        ('DBSCAN', dbscan_labels),
        ('PCA + K-Means', pca_results['kmeans_labels']),
        ('PCA + GMM', pca_results['gmm_labels'])
    ]
    
    for i, (method_name, labels) in enumerate(methods):
        row, col = i // 3, i % 3
        if i < 3:  # Original space visualization
            scatter = axes[row, col].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
        else:  # PCA space visualization
            scatter = axes[row, col].scatter(pca_results['pca_features'][:, 0], pca_results['pca_features'][:, 1], 
                                           c=labels, cmap='viridis', alpha=0.6)
        
        axes[row, col].set_title(method_name)
        axes[row, col].set_xlabel('PC1' if i < 3 else 'PC1 (PCA)')
        axes[row, col].set_ylabel('PC2' if i < 3 else 'PC2 (PCA)')
    
    # Silhouette score comparison
    valid_methods = []
    valid_scores = []
    for method, score in zip(methods_data['Method'], methods_data['Silhouette Score']):
        if score != 'N/A':
            valid_methods.append(method)
            valid_scores.append(float(score))
    
    axes[1, 2].bar(valid_methods, valid_scores, color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
    axes[1, 2].set_title('Silhouette Score Comparison')
    axes[1, 2].set_ylabel('Silhouette Score')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    # Add score values on bars
    for i, score in enumerate(valid_scores):
        axes[1, 2].text(i, score + 0.01, f'{score:.3f}', ha='center')
    
    plt.tight_layout()
    plt.show()
    
    # Find best method
    best_method_idx = np.argmax(valid_scores)
    best_method = valid_methods[best_method_idx]
    best_score = valid_scores[best_method_idx]
    
    print(f"\n🏆 BEST METHOD: {best_method}")
    print(f"   Silhouette Score: {best_score:.4f}")
    
    # Recommendations
    print("\n📊 RECOMMENDATIONS:")
    print("   • For interpretability: K-Means (clear centroids, easy to understand)")
    print("   • For probabilistic clustering: GMM (provides cluster probabilities)")
    print("   • For noise detection: DBSCAN (identifies outliers automatically)")
    print("   • For dimensionality reduction: PCA + clustering (reduces complexity)")
    
    return comparison_df

# Run comprehensive comparison
comparison_results = compare_all_methods()

def compare_all_methods():
    """Compare all clustering methods"""
    print("\n" + "="*60)
    print("COMPREHENSIVE COMPARISON OF ALL CLUSTERING METHODS")
    print("="*60)
    
    # Compile results
    methods_data = {
        'Method': ['K-Means', 'GMM', 'DBSCAN', 'PCA + K-Means', 'PCA + GMM'],
        'Silhouette Score': [
            kmeans_metrics['silhouette'],
            gmm_metrics['silhouette'],
            dbscan_metrics['silhouette'] if dbscan_metrics['silhouette'] > -1 else 'N/A',
            pca_results['kmeans_silhouette'],
            pca_results['gmm_silhouette']
        ],
        'Calinski-Harabasz': [
            kmeans_metrics['calinski'],
            gmm_metrics['calinski'],
            dbscan_metrics['calinski'] if dbscan_metrics['calinski'] > -1 else 'N/A',
            'N/A',  # Not calculated for PCA methods in this example
            'N/A'
        ],
        'Special Metrics': [
            f"Inertia: {kmeans_metrics['inertia']:.0f}",
            f"AIC: {gmm_metrics['aic']:.0f}, BIC: {gmm_metrics['bic']:.0f}",
            f"Clusters: {dbscan_metrics['n_clusters']}, Noise: {dbscan_metrics['n_noise']}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}"
        ]
    }
    
    # Create comparison DataFrame
    comparison_df = pd.DataFrame(methods_data)
    print("\nMethod Comparison Table:")
    print(comparison_df.to_string(index=False))
    
    # Create comparison visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Comprehensive Method Comparison', fontsize=16, fontweight='bold')
    
    # PCA for consistent visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Plot all methods
    methods = [
        ('K-Means', kmeans_labels),
        ('GMM', gmm_labels),
        ('DBSCAN', dbscan_labels),
        ('PCA + K-Means', pca_results['kmeans_labels']),
        ('PCA + GMM', pca_results['gmm_labels'])
    ]
    
    for i, (method_name, labels) in enumerate(methods):
        row, col = i // 3, i % 3
        if i < 3:  # Original space visualization
            scatter = axes[row, col].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
        else:  # PCA space visualization
            scatter = axes[row, col].scatter(pca_results['pca_features'][:, 0], pca_results['pca_features'][:, 1], 
                                           c=labels, cmap='viridis', alpha=0.6)
        
        axes[row, col].set_title(method_name)
        axes[row, col].set_xlabel('PC1' if i < 3 else 'PC1 (PCA)')
        axes[row, col].set_ylabel('PC2' if i < 3 else 'PC2 (PCA)')
    
    # Silhouette score comparison
    valid_methods = []
    valid_scores = []
    for method, score in zip(methods_data['Method'], methods_data['Silhouette Score']):
        if score != 'N/A':
            valid_methods.append(method)
            valid_scores.append(float(score))
    
    axes[1, 2].bar(valid_methods, valid_scores, color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
    axes[1, 2].set_title('Silhouette Score Comparison')
    axes[1, 2].set_ylabel('Silhouette Score')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    # Add score values on bars
    for i, score in enumerate(valid_scores):
        axes[1, 2].text(i, score + 0.01, f'{score:.3f}', ha='center')
    
    plt.tight_layout()
    plt.show()
    
    # Find best method
    best_method_idx = np.argmax(valid_scores)
    best_method = valid_methods[best_method_idx]
    best_score = valid_scores[best_method_idx]
    
    print(f"\n🏆 BEST METHOD: {best_method}")
    print(f"   Silhouette Score: {best_score:.4f}")
    
    # Recommendations
    print("\n📊 RECOMMENDATIONS:")
    print("   • For interpretability: K-Means (clear centroids, easy to understand)")
    print("   • For probabilistic clustering: GMM (provides cluster probabilities)")
    print("   • For noise detection: DBSCAN (identifies outliers automatically)")
    print("   • For dimensionality reduction: PCA + clustering (reduces complexity)")
    
    return comparison_df

# Run comprehensive comparison
comparison_results = compare_all_methods()

def compare_all_methods():
    """Compare all clustering methods"""
    print("\n" + "="*60)
    print("COMPREHENSIVE COMPARISON OF ALL CLUSTERING METHODS")
    print("="*60)
    
    # Compile results
    methods_data = {
        'Method': ['K-Means', 'GMM', 'DBSCAN', 'PCA + K-Means', 'PCA + GMM'],
        'Silhouette Score': [
            kmeans_metrics['silhouette'],
            gmm_metrics['silhouette'],
            dbscan_metrics['silhouette'] if dbscan_metrics['silhouette'] > -1 else 'N/A',
            pca_results['kmeans_silhouette'],
            pca_results['gmm_silhouette']
        ],
        'Calinski-Harabasz': [
            kmeans_metrics['calinski'],
            gmm_metrics['calinski'],
            dbscan_metrics['calinski'] if dbscan_metrics['calinski'] > -1 else 'N/A',
            'N/A',  # Not calculated for PCA methods in this example
            'N/A'
        ],
        'Special Metrics': [
            f"Inertia: {kmeans_metrics['inertia']:.0f}",
            f"AIC: {gmm_metrics['aic']:.0f}, BIC: {gmm_metrics['bic']:.0f}",
            f"Clusters: {dbscan_metrics['n_clusters']}, Noise: {dbscan_metrics['n_noise']}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}",
            f"Explained Var: {pca_results['explained_variance'].sum():.3f}"
        ]
    }
    
    # Create comparison DataFrame
    comparison_df = pd.DataFrame(methods_data)
    print("\nMethod Comparison Table:")
    print(comparison_df.to_string(index=False))
    
    # Create comparison visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Comprehensive Method Comparison', fontsize=16, fontweight='bold')
    
    # PCA for consistent visualization
    pca_viz = PCA(n_components=2, random_state=42)
    features_2d = pca_viz.fit_transform(features)
    
    # Plot all methods
    methods = [
        ('K-Means', kmeans_labels),
        ('GMM', gmm_labels),
        ('DBSCAN', dbscan_labels),
        ('PCA + K-Means', pca_results['kmeans_labels']),
        ('PCA + GMM', pca_results['gmm_labels'])
    ]
    
    for i, (method_name, labels) in enumerate(methods):
        row, col = i // 3, i % 3
        if i < 3:  # Original space visualization
            scatter = axes[row, col].scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)
        else:  # PCA space visualization
            scatter = axes[row, col].scatter(pca_results['pca_features'][:, 0], pca_results['pca_features'][:, 1], 
                                           c=labels, cmap='viridis', alpha=0.6)
        
        axes[row, col].set_title(method_name)
        axes[row, col].set_xlabel('PC1' if i < 3 else 'PC1 (PCA)')
        axes[row, col].set_ylabel('PC2' if i < 3 else 'PC2 (PCA)')
    
    # Silhouette score comparison
    valid_methods = []
    valid_scores = []
    for method, score in zip(methods_data['Method'], methods_data['Silhouette Score']):
        if score != 'N/A':
            valid_methods.append(method)
            valid_scores.append(float(score))
    
    axes[1, 2].bar(valid_methods, valid_scores, color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
    axes[1, 2].set_title('Silhouette Score Comparison')
    axes[1, 2].set_ylabel('Silhouette Score')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    # Add score values on bars
    for i, score in enumerate(valid_scores):
        axes[1, 2].text(i, score + 0.01, f'{score:.3f}', ha='center')
    
    plt.tight_layout()
    plt.show()
    
    # Find best method
    best_method_idx = np.argmax(valid_scores)
    best_method = valid_methods[best_method_idx]
    best_score = valid_scores[best_method_idx]
    
    print(f"\n🏆 BEST METHOD: {best_method}")
    print(f"   Silhouette Score: {best_score:.4f}")
    
    # Recommendations
    print("\n📊 RECOMMENDATIONS:")
    print("   • For interpretability: K-Means (clear centroids, easy to understand)")
    print("   • For probabilistic clustering: GMM (provides cluster probabilities)")
    print("   • For noise detection: DBSCAN (identifies outliers automatically)")
    print("   • For dimensionality reduction: PCA + clustering (reduces complexity)")
    
    return comparison_df

# Run comprehensive comparison
comparison_results = compare_all_methods()
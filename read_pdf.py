import PyPDF2
import sys
import os

def read_pdf(pdf_path):
    """Read and extract text from PDF file"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            print(f"PDF Document: {os.path.basename(pdf_path)}")
            print(f"Number of pages: {len(pdf_reader.pages)}")
            print("="*80)
            
            full_text = ""
            for page_num, page in enumerate(pdf_reader.pages):
                print(f"\n--- PAGE {page_num + 1} ---")
                page_text = page.extract_text()
                print(page_text)
                full_text += f"\n--- PAGE {page_num + 1} ---\n" + page_text
                print("-" * 40)
            
            return full_text
            
    except Exception as e:
        print(f"Error reading PDF: {e}")
        return None

if __name__ == "__main__":
    pdf_path = "CrysID_MLME25.pdf"
    if os.path.exists(pdf_path):
        text = read_pdf(pdf_path)
        
        # Save extracted text to file for further analysis
        with open("CrysID_extracted_text.txt", "w", encoding="utf-8") as f:
            f.write(text)
        print(f"\nText extracted and saved to CrysID_extracted_text.txt")
    else:
        print(f"PDF file not found: {pdf_path}")

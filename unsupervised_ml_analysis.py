import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.cluster import KMeans, AgglomerativeClustering, DBSCAN
from sklearn.mixture import GaussianMixture
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.metrics import silhouette_score, adjusted_rand_score, calinski_harabasz_score
from scipy.cluster.hierarchy import dendrogram, linkage
from scipy.spatial.distance import pdist
import glob
import os
import warnings
warnings.filterwarnings('ignore')

# Set style for better plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class UnsupervisedMLAnalysis:
    def __init__(self, data_path="data"):
        self.data_path = data_path
        self.raw_data = []
        self.processed_data = None
        self.feature_names = []
        self.scaler = StandardScaler()
        self.results = {}
        
    def load_data(self):
        """Load all data files and combine them into a single dataset"""
        print("Loading data from all files...")
        
        # Get all txt files in data directory
        file_pattern = os.path.join(self.data_path, "*.txt")
        files = glob.glob(file_pattern)
        
        print(f"Found {len(files)} files to process")
        
        all_data = []
        file_info = []
        
        for i, file_path in enumerate(files):
            try:
                # Read the file
                df = pd.read_csv(file_path, sep='\t')
                
                # Add file identifier
                df['file_id'] = i
                df['file_name'] = os.path.basename(file_path)
                
                all_data.append(df)
                file_info.append({
                    'file_id': i,
                    'file_name': os.path.basename(file_path),
                    'num_rows': len(df),
                    'num_cols': len(df.columns)
                })
                
                if i % 10 == 0:
                    print(f"Processed {i+1}/{len(files)} files")
                    
            except Exception as e:
                print(f"Error reading {file_path}: {e}")
                continue
        
        # Combine all data
        self.raw_data = pd.concat(all_data, ignore_index=True)
        self.file_info = pd.DataFrame(file_info)
        
        print(f"Successfully loaded {len(all_data)} files")
        print(f"Total data shape: {self.raw_data.shape}")
        print(f"Columns: {list(self.raw_data.columns)}")
        
        return self.raw_data
    
    def explore_data(self):
        """Explore the structure and characteristics of the data"""
        print("\n" + "="*50)
        print("DATA EXPLORATION")
        print("="*50)
        
        # Basic info
        print(f"Dataset shape: {self.raw_data.shape}")
        print(f"Number of files: {self.raw_data['file_id'].nunique()}")
        print(f"Average rows per file: {len(self.raw_data) / self.raw_data['file_id'].nunique():.1f}")
        
        # Column information
        print(f"\nColumns ({len(self.raw_data.columns)}):")
        for col in self.raw_data.columns:
            print(f"  - {col}")
        
        # Data types
        print(f"\nData types:")
        print(self.raw_data.dtypes)
        
        # Missing values
        print(f"\nMissing values:")
        missing = self.raw_data.isnull().sum()
        if missing.sum() > 0:
            print(missing[missing > 0])
        else:
            print("No missing values found")
        
        # Basic statistics for numeric columns
        numeric_cols = self.raw_data.select_dtypes(include=[np.number]).columns
        print(f"\nNumeric columns ({len(numeric_cols)}):")
        for col in numeric_cols:
            if col not in ['file_id']:
                print(f"  - {col}")
        
        # Display basic statistics
        print(f"\nBasic statistics for key variables:")
        key_vars = [col for col in numeric_cols if col not in ['file_id']][:10]  # First 10 numeric columns
        print(self.raw_data[key_vars].describe())
        
        return self.raw_data.describe()
    
    def preprocess_data(self):
        """Preprocess data for machine learning"""
        print("\n" + "="*50)
        print("DATA PREPROCESSING")
        print("="*50)
        
        # Select numeric columns for clustering (exclude identifiers)
        numeric_cols = self.raw_data.select_dtypes(include=[np.number]).columns
        feature_cols = [col for col in numeric_cols if col not in ['file_id']]
        
        print(f"Selected {len(feature_cols)} features for clustering:")
        for col in feature_cols:
            print(f"  - {col}")
        
        # Extract features
        features = self.raw_data[feature_cols].copy()
        
        # Handle any remaining missing values
        if features.isnull().sum().sum() > 0:
            print("Handling missing values...")
            features = features.fillna(features.mean())
        
        # Remove any infinite values
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.mean())
        
        # Store feature names
        self.feature_names = feature_cols
        
        # Scale the features
        print("Scaling features...")
        scaled_features = self.scaler.fit_transform(features)
        
        # Create processed dataframe
        self.processed_data = pd.DataFrame(
            scaled_features, 
            columns=feature_cols,
            index=features.index
        )
        
        # Add file information back
        self.processed_data['file_id'] = self.raw_data['file_id']
        self.processed_data['file_name'] = self.raw_data['file_name']
        
        print(f"Processed data shape: {self.processed_data.shape}")
        print(f"Features shape for clustering: {scaled_features.shape}")
        
        return self.processed_data, scaled_features

    def feature_engineering(self):
        """Create additional features for better clustering"""
        print("\n" + "="*50)
        print("FEATURE ENGINEERING")
        print("="*50)

        # Get the scaled features (excluding file info)
        feature_cols = [col for col in self.processed_data.columns if col not in ['file_id', 'file_name']]
        features = self.processed_data[feature_cols].copy()

        # Create additional engineered features
        print("Creating engineered features...")

        # Temperature differences
        features['temp_diff_PM_TM'] = features['T_PM'] - features['T_TM']
        features['temp_diff_PM_in'] = features['T_PM'] - features['T_PM_in']
        features['temp_diff_TM_in'] = features['T_TM'] - features['T_TM_in']

        # Particle size ratios
        features['d90_d10_ratio'] = features['d90'] / (features['d10'] + 1e-8)  # Add small value to avoid division by zero
        features['d50_d10_ratio'] = features['d50'] / (features['d10'] + 1e-8)
        features['d90_d50_ratio'] = features['d90'] / (features['d50'] + 1e-8)

        # Mass flow ratio
        features['mf_ratio'] = features['mf_PM'] / (features['mf_TM'] + 1e-8)

        # Concentration features
        features['c_diff'] = features['c'] - features['c_in']

        # Crystal growth rate proxy
        features['crystal_growth_rate'] = features['w_crystal'] * features['Q_g']

        print(f"Original features: {len(feature_cols)}")
        print(f"Total features after engineering: {features.shape[1]}")
        print(f"New engineered features: {features.shape[1] - len(feature_cols)}")

        # Remove any infinite or NaN values that might have been created
        features = features.replace([np.inf, -np.inf], np.nan)
        features = features.fillna(features.mean())

        return features

    def apply_kmeans(self, features, n_clusters=2):
        """Apply K-Means clustering"""
        print(f"\nApplying K-Means clustering (k={n_clusters})...")

        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        labels = kmeans.fit_predict(features)

        # Calculate metrics
        silhouette = silhouette_score(features, labels)
        inertia = kmeans.inertia_
        calinski = calinski_harabasz_score(features, labels)

        results = {
            'method': 'K-Means',
            'labels': labels,
            'silhouette_score': silhouette,
            'inertia': inertia,
            'calinski_harabasz': calinski,
            'cluster_centers': kmeans.cluster_centers_,
            'n_clusters': n_clusters
        }

        print(f"  Silhouette Score: {silhouette:.4f}")
        print(f"  Inertia: {inertia:.4f}")
        print(f"  Calinski-Harabasz Score: {calinski:.4f}")

        return results

    def apply_hierarchical(self, features, n_clusters=2):
        """Apply Hierarchical clustering"""
        print(f"\nApplying Hierarchical clustering (k={n_clusters})...")

        hierarchical = AgglomerativeClustering(n_clusters=n_clusters, linkage='ward')
        labels = hierarchical.fit_predict(features)

        # Calculate metrics
        silhouette = silhouette_score(features, labels)
        calinski = calinski_harabasz_score(features, labels)

        results = {
            'method': 'Hierarchical',
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz': calinski,
            'n_clusters': n_clusters
        }

        print(f"  Silhouette Score: {silhouette:.4f}")
        print(f"  Calinski-Harabasz Score: {calinski:.4f}")

        return results

    def apply_gmm(self, features, n_components=2):
        """Apply Gaussian Mixture Model clustering"""
        print(f"\nApplying Gaussian Mixture Model (k={n_components})...")

        gmm = GaussianMixture(n_components=n_components, random_state=42)
        labels = gmm.fit_predict(features)

        # Calculate metrics
        silhouette = silhouette_score(features, labels)
        calinski = calinski_harabasz_score(features, labels)
        aic = gmm.aic(features)
        bic = gmm.bic(features)

        results = {
            'method': 'GMM',
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz': calinski,
            'aic': aic,
            'bic': bic,
            'n_clusters': n_components
        }

        print(f"  Silhouette Score: {silhouette:.4f}")
        print(f"  Calinski-Harabasz Score: {calinski:.4f}")
        print(f"  AIC: {aic:.4f}")
        print(f"  BIC: {bic:.4f}")

        return results

    def apply_dbscan(self, features, eps=0.5, min_samples=5):
        """Apply DBSCAN clustering"""
        print(f"\nApplying DBSCAN clustering (eps={eps}, min_samples={min_samples})...")

        dbscan = DBSCAN(eps=eps, min_samples=min_samples)
        labels = dbscan.fit_predict(features)

        n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
        n_noise = list(labels).count(-1)

        print(f"  Number of clusters: {n_clusters}")
        print(f"  Number of noise points: {n_noise}")

        # Calculate metrics only if we have more than 1 cluster
        if n_clusters > 1:
            # Remove noise points for silhouette calculation
            mask = labels != -1
            if mask.sum() > 0:
                silhouette = silhouette_score(features[mask], labels[mask])
                calinski = calinski_harabasz_score(features[mask], labels[mask])
            else:
                silhouette = -1
                calinski = -1
        else:
            silhouette = -1
            calinski = -1

        results = {
            'method': 'DBSCAN',
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz': calinski,
            'n_clusters': n_clusters,
            'n_noise': n_noise,
            'eps': eps,
            'min_samples': min_samples
        }

        if silhouette > -1:
            print(f"  Silhouette Score: {silhouette:.4f}")
            print(f"  Calinski-Harabasz Score: {calinski:.4f}")
        else:
            print("  Could not calculate silhouette score (insufficient clusters)")

        return results

    def apply_pca_clustering(self, features, n_components=2, clustering_method='kmeans'):
        """Apply PCA for dimensionality reduction then cluster"""
        print(f"\nApplying PCA + {clustering_method.upper()} clustering...")

        # Apply PCA
        pca = PCA(n_components=n_components, random_state=42)
        features_pca = pca.fit_transform(features)

        print(f"  Explained variance ratio: {pca.explained_variance_ratio_}")
        print(f"  Total explained variance: {pca.explained_variance_ratio_.sum():.4f}")

        # Apply clustering on PCA features
        if clustering_method.lower() == 'kmeans':
            cluster_result = self.apply_kmeans(features_pca, n_clusters=2)
        elif clustering_method.lower() == 'hierarchical':
            cluster_result = self.apply_hierarchical(features_pca, n_clusters=2)
        elif clustering_method.lower() == 'gmm':
            cluster_result = self.apply_gmm(features_pca, n_components=2)

        cluster_result['method'] = f'PCA + {clustering_method.upper()}'
        cluster_result['pca_features'] = features_pca
        cluster_result['pca_explained_variance'] = pca.explained_variance_ratio_
        cluster_result['pca_components'] = pca.components_

        return cluster_result

    def compare_methods(self, features):
        """Compare all clustering methods"""
        print("\n" + "="*50)
        print("COMPARING ALL CLUSTERING METHODS")
        print("="*50)

        results = {}

        # For memory-intensive methods, use a sample
        sample_size = min(10000, len(features))
        print(f"Using sample size of {sample_size} for memory-intensive methods")

        # Random sample for hierarchical clustering and DBSCAN
        np.random.seed(42)
        sample_indices = np.random.choice(len(features), sample_size, replace=False)
        features_sample = features[sample_indices]

        # Apply all methods
        print(f"\nApplying methods on full dataset ({len(features)} samples):")
        results['kmeans'] = self.apply_kmeans(features)
        results['gmm'] = self.apply_gmm(features)

        print(f"\nApplying methods on sample dataset ({len(features_sample)} samples):")
        results['hierarchical'] = self.apply_hierarchical(features_sample)

        # Try different DBSCAN parameters on sample
        eps_values = [0.1, 0.5, 1.0, 2.0]
        best_dbscan = None
        best_silhouette = -1

        for eps in eps_values:
            dbscan_result = self.apply_dbscan(features_sample, eps=eps, min_samples=5)
            if dbscan_result['silhouette_score'] > best_silhouette and dbscan_result['n_clusters'] >= 2:
                best_dbscan = dbscan_result
                best_silhouette = dbscan_result['silhouette_score']

        if best_dbscan:
            results['dbscan'] = best_dbscan

        # PCA-based clustering on full dataset
        print(f"\nApplying PCA methods on full dataset:")
        results['pca_kmeans'] = self.apply_pca_clustering(features, n_components=2, clustering_method='kmeans')
        results['pca_gmm'] = self.apply_pca_clustering(features, n_components=2, clustering_method='gmm')

        # PCA + hierarchical on sample
        print(f"\nApplying PCA + Hierarchical on sample dataset:")
        results['pca_hierarchical'] = self.apply_pca_clustering(features_sample, n_components=2, clustering_method='hierarchical')

        self.results = results
        return results

    def print_summary(self):
        """Print summary of all results"""
        print("\n" + "="*50)
        print("CLUSTERING RESULTS SUMMARY")
        print("="*50)

        summary_data = []
        for method_name, result in self.results.items():
            summary_data.append({
                'Method': result['method'],
                'Silhouette Score': f"{result['silhouette_score']:.4f}" if result['silhouette_score'] > -1 else "N/A",
                'Calinski-Harabasz': f"{result['calinski_harabasz']:.4f}" if result['calinski_harabasz'] > -1 else "N/A",
                'N Clusters': result['n_clusters'],
                'Additional Info': self._get_additional_info(result)
            })

        summary_df = pd.DataFrame(summary_data)
        print(summary_df.to_string(index=False))

        # Find best method based on silhouette score
        valid_results = {k: v for k, v in self.results.items() if v['silhouette_score'] > -1}
        if valid_results:
            best_method = max(valid_results.keys(), key=lambda x: valid_results[x]['silhouette_score'])
            print(f"\nBest method based on Silhouette Score: {self.results[best_method]['method']}")
            print(f"Silhouette Score: {self.results[best_method]['silhouette_score']:.4f}")

    def _get_additional_info(self, result):
        """Get additional information for each method"""
        if result['method'] == 'K-Means':
            return f"Inertia: {result['inertia']:.2f}"
        elif result['method'] == 'GMM':
            return f"AIC: {result['aic']:.2f}, BIC: {result['bic']:.2f}"
        elif result['method'] == 'DBSCAN':
            return f"eps: {result['eps']}, noise: {result['n_noise']}"
        elif 'PCA' in result['method']:
            return f"Explained var: {result['pca_explained_variance'].sum():.3f}"
        else:
            return ""

if __name__ == "__main__":
    # Initialize analysis
    analysis = UnsupervisedMLAnalysis()

    # Load and explore data
    print("Step 1: Loading and exploring data...")
    raw_data = analysis.load_data()
    stats = analysis.explore_data()
    processed_data, scaled_features = analysis.preprocess_data()

    # Feature engineering
    print("\nStep 2: Feature engineering...")
    engineered_features = analysis.feature_engineering()

    # Apply all clustering methods
    print("\nStep 3: Applying clustering methods...")
    results = analysis.compare_methods(engineered_features.values)

    # Print summary
    print("\nStep 4: Results summary...")
    analysis.print_summary()

    print("\n" + "="*50)
    print("ANALYSIS COMPLETED SUCCESSFULLY!")
    print("="*50)

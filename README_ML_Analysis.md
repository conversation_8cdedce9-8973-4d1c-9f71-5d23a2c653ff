# Unsupervised Machine Learning Analysis

This project implements comprehensive unsupervised machine learning analysis to classify data from 98 files (each containing 1000 steps with multiple categories) into 2 categories using multiple clustering methods.

## 📁 Files Created

1. **`unsupervised_ml_visualization.ipynb`** - Main Jupyter notebook with all visualizations and analysis
2. **`unsupervised_ml_analysis.py`** - Python script version (alternative to notebook)
3. **`README_ML_Analysis.md`** - This documentation file

## 🔬 Methods Implemented

### 1. K-Means Clustering
- **Purpose**: Partition data into k clusters using centroids
- **Visualization**: Cluster scatter plots, silhouette analysis, elbow method
- **Metrics**: Silhouette score, Calinski-Harabasz score, inertia

### 2. Gaussian Mixture Model (GMM)
- **Purpose**: Probabilistic clustering with soft assignments
- **Visualization**: Cluster plots, AIC/BIC model selection, probability distributions
- **Metrics**: Silhouette score, AIC, BIC scores

### 3. DBSCAN
- **Purpose**: Density-based clustering that identifies noise/outliers
- **Visualization**: Cluster plots, parameter sensitivity analysis, noise detection
- **Metrics**: Silhouette score, number of clusters, noise points

### 4. Hierarchical Clustering
- **Purpose**: Create hierarchical cluster structure
- **Visualization**: Dendrogram, cluster plots, silhouette analysis
- **Metrics**: Silhouette score, Calinski-Harabasz score

### 5. PCA + Clustering
- **Purpose**: Dimensionality reduction followed by clustering
- **Visualization**: PCA components, feature importance, reduced-space clustering
- **Metrics**: Explained variance, clustering metrics in reduced space

## 🚀 How to Run

### Option 1: Jupyter Notebook (Recommended)
```bash
# Install required packages
pip install pandas numpy matplotlib seaborn scikit-learn scipy jupyter

# Start Jupyter
jupyter notebook

# Open unsupervised_ml_visualization.ipynb
# Run all cells sequentially
```

### Option 2: Python Script
```bash
# Install required packages
pip install pandas numpy matplotlib seaborn scikit-learn scipy

# Run the analysis
python unsupervised_ml_analysis.py
```

## 📊 Features

### Data Processing
- **Automatic file loading**: Processes all 98 .txt files in the data directory
- **Feature engineering**: Creates additional meaningful features from raw data
- **Data cleaning**: Handles missing values, infinite values, and outliers
- **Standardization**: Scales features for optimal clustering performance

### Advanced Features
- **Memory management**: Uses sampling for memory-intensive algorithms
- **Parameter optimization**: Tests multiple parameter combinations
- **Comprehensive metrics**: Multiple evaluation criteria for each method
- **Interactive visualizations**: Detailed plots for each clustering method

### Visualizations Created
1. **Data exploration plots**: Distribution histograms, correlation matrices
2. **Individual method plots**: 4-panel visualization for each clustering method
3. **Comparison plots**: Side-by-side comparison of all methods
4. **Performance metrics**: Bar charts comparing silhouette scores
5. **Specialized plots**: Dendrograms, elbow curves, PCA components

## 📈 Output and Results

### For Each Method:
- **Cluster visualization** in 2D PCA space
- **Performance metrics** (silhouette score, etc.)
- **Method-specific analysis** (e.g., elbow curve for K-means)
- **Cluster size distribution**

### Comprehensive Comparison:
- **Side-by-side cluster visualizations**
- **Performance metrics table**
- **Best method recommendation**
- **Method-specific insights and recommendations**

## 🎯 Key Insights

The analysis provides:
- **Optimal clustering method** based on silhouette scores
- **Data structure insights** through various clustering approaches
- **Feature importance** analysis via PCA
- **Noise detection** capabilities through DBSCAN
- **Probabilistic assignments** through GMM

## 🔧 Customization

### Modify Parameters:
- **Sample size**: Adjust `sample_size` parameter for memory management
- **Number of clusters**: Change `n_clusters` parameter (currently set to 2)
- **DBSCAN parameters**: Modify `eps` and `min_samples`
- **PCA components**: Adjust `n_components` for dimensionality reduction

### Add New Methods:
The notebook structure allows easy addition of new clustering methods by following the existing pattern:
1. Create `apply_and_visualize_[method]()` function
2. Add to comparison section
3. Update metrics compilation

## 📋 Requirements

```
pandas>=1.3.0
numpy>=1.21.0
matplotlib>=3.4.0
seaborn>=0.11.0
scikit-learn>=1.0.0
scipy>=1.7.0
jupyter>=1.0.0 (for notebook)
```

## 🎨 Visualization Features

- **Consistent color schemes** across all plots
- **High-resolution figures** suitable for presentations
- **Interactive elements** in Jupyter environment
- **Professional styling** with seaborn themes
- **Comprehensive legends** and annotations

## 💡 Tips for Best Results

1. **Data Quality**: Ensure your data files are properly formatted (tab-separated)
2. **Memory**: Adjust sample size if running into memory issues
3. **Interpretation**: Consider domain knowledge when interpreting clusters
4. **Validation**: Cross-validate results with different random seeds
5. **Feature Selection**: Experiment with different feature engineering approaches

## 🔍 Troubleshooting

- **Memory errors**: Reduce `sample_size` parameter
- **Import errors**: Install missing packages using pip
- **File not found**: Ensure data files are in the `data/` directory
- **Poor clustering**: Try different preprocessing or feature engineering approaches

This comprehensive analysis provides multiple perspectives on your data structure and helps identify the most appropriate clustering approach for your specific dataset.
